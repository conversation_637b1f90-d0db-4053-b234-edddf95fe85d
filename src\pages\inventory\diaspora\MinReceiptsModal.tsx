import { DataTable } from "@/components/custom/tables/Table1";
import React, { useState } from "react";
import ReceiptDetailsModal from "./ReceiptDetailsModal";
import ConfirmationModal from "@/components/custom/modals/ConfirmationModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  useGetDiasporaRecieptsQuery,
  useLazyGetDiasporaRecieptsReportQuery,
  usePatchDiasporaRecieptsMutation,
} from "@/redux/slices/projects";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Eye, Loader2, X } from "lucide-react";
import { searchDebouncer } from "@/utils/debouncers";
import { formatDateTime } from "@/utils/formatDate";
import BaseModal from "@/components/custom/modals/BaseModal";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { contentHeader } from "@/utils/header";
import getStoredState from "redux-persist/es/getStoredState";
import { RootState, store } from "@/redux/store";

type propTypes = {
  trip_id: number;
};

const MinReceiptsModal = ({ trip_id }: propTypes) => {
  const [reason, setReason] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to API
  const [selectedBooking, setSelectedBooking] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  const [selectedReceiptId, setSelectedReceiptId] = useState<string | null>(
    null
  );
  const [selectedReceiptPlot, setSelectedReceiptPlot] = useState<string | null>(
    null
  );
  const [isOpenCancelModal, setIsOpenCancelModal] = useState(false);

  const { data: bookings, isLoading } = useGetDiasporaRecieptsQuery({
    trip_id: trip_id,
  });
  const [cancelReceipt, { isLoading: cancelling }] =
    usePatchDiasporaRecieptsMutation();

  const handleCancelReciept = async () => {
    if (selectedReceiptId) {
      const res = await cancelReceipt({
        id: selectedReceiptId,
        action: "cancel",
        reason,
      }).unwrap();
      if (res?.success) {
        toast.success("Receipt cancelled successfully");
        setSelectedReceiptId(null);
        setIsOpenCancelModal(false);
      } else {
        toast.error("Failed to cancel receipt");
        return;
      }
    } else {
      toast.error("Please select a receipt to cancel");
      return;
    }
  };

  const handleConfirmCancel = (id: string, plot: string) => {
    setSelectedReceiptId(id);
    setSelectedReceiptPlot(plot);
    setIsOpenCancelModal(true);
  };

  useState(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 768);
    };
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  });

  const openDetailsModal = (booking: any) => {
    setSelectedBooking(booking);
    setIsModalOpen(true);
  };

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: "plot_no",
      header: "Plot No",
      cell: (info) => (
        <span className="font-medium">{info.getValue() as string}</span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "marketer",
      header: "Marketer",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "client_name",
      header: "Client",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "amount",
      header: "Amount Paid",
      cell: (info) => {
        const booking = info.row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">
              {(info.getValue() as number).toLocaleString()}
            </span>
            <span className="text-xs text-gray-500">
              Pay Mode - {booking?.payment_mode}
            </span>
            {booking.acc_no != "N/A" && (
              <span className="text-xs text-gray-500">
                A/c No - {booking.acc_no}
              </span>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "receipt",
      header: "Attachments",
      cell: (info) => {
        const booking = info.row.original;
        return (
          <div className="flex flex-col space-y-1">
            {booking.receipt && (
              <a
                href={booking.receipt}
                target="_blank"
                rel="noopener noreferrer"
                download
                className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
              >
                <span className="material-icons text-blue-500 mr-1 text-xl">
                  &#x1F4CE;
                </span>
                Open Proof Of Payment
              </a>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "country",
      header: "Country",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "is_cancelled",
      header: "Status",
      cell: (info) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-semibold ${
            info.getValue() === true
              ? "bg-red-100 text-red-800"
              : "bg-green-100 text-green-800"
          }`}
        >
          {info.getValue() === true ? "Cancelled" : "Active"}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell: (info) => formatDateTime(info.getValue() as string),
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e: any) => {
                e.stopPropagation();
                openDetailsModal(booking);
              }}
              className="h-8 w-8 p-0"
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">View details</span>
            </Button>

            {!booking.is_cancelled && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleConfirmCancel(booking.id, booking.plot_no);
                }}
                className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Cancel Receipt</span>
              </Button>
            )}
          </>
        );
      },
      enableColumnFilter: false,
    },
  ];

  const [downloadExcel, { isLoading: downloading }] =
    useLazyGetDiasporaRecieptsReportQuery();

  const handleDownloadExcel = async (trip_id: number) => {
    try {
      const res = await downloadExcel({
        trip_id: trip_id,
        export: "excel",
      }).unwrap();
      if (res) {
        console.log("Downloaded", res);
        downloadReceiptsExcel(res);
      }
    } catch (error) {
      console.error("Error:", error);
      return;
    }
  };

  const downloadReceiptsExcel = async (trip_id: number) => {
    const token = (store.getState() as RootState).auth.token?.AccessToken;
    const environment = import.meta.env.VITE_PROD;
    const base_url =
      environment == "production"
        ? import.meta.env.VITE_API_URL_PROD
        : import.meta.env.VITE_API_URL_DEV;

    const url = `${base_url}/leads/diapora-trip-receipts-report?trip_id=${trip_id}&export=excel`;
    const response = await fetch(url, {
      method: "GET",
      headers: { Authorization: `Token ${token}` },
    });

    if (!response.ok) {
      throw new Error("Failed to download file");
    }

    const blob = await response.blob();

    // Extract filename from headers
    const contentDisposition = response.headers.get("content-disposition");
    let filename = "report.xlsx";
    if (contentDisposition && contentDisposition.includes("filename=")) {
      filename = contentDisposition
        .split("filename=")[1]
        .replace(/["']/g, "")
        .trim();
    }

    const blobUrl = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = blobUrl;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(blobUrl);
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border dark:bg-gray-800 dark:text-white">
        <div className="p-2 md:p-4">
          <div className="overflow-x-auto">
            <div className="w-full flex justify-end py-2">
              {downloading ? (
                <PrimaryButton type="button" disabled>
                  Download...
                </PrimaryButton>
              ) : (
                <PrimaryButton
                  type="button"
                  onClick={() => downloadReceiptsExcel(trip_id)}
                >
                  Download Excel
                </PrimaryButton>
              )}
            </div>
            <DataTable<any>
              data={bookings?.data ? bookings.data.results : []}
              columns={columns}
              enableToolbar={true}
              enableExportToExcel={false}
              enablePrintPdf={false}
              enablePagination={true}
              enableColumnFilters={false}
              enableSorting={true}
              tHeadClassName="bg-secondary"
              searchInput={
                <input
                  value={searchInput}
                  name="searchInput"
                  type="search"
                  onChange={(e) =>
                    searchDebouncer(
                      e.target.value,
                      setSearchInput,
                      setSearchValue
                    )
                  }
                  className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Search..."
                />
              }
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              itemsPerPage={itemsPerPage}
              setItemsPerPage={setItemsPerPage}
              totalItems={bookings?.data?.total_data || 0}
            />
          </div>
        </div>
      </div>
      <ReceiptDetailsModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        selectedBooking={selectedBooking}
        isOpenCancelModal={isOpenCancelModal}
        setIsOpenCancelModal={setIsOpenCancelModal}
        handleConfirmCancel={handleConfirmCancel}
        handleCancelReciept={handleCancelReciept}
        // openDetailsModal={openDetailsModal}
      />

      <BaseModal
        isOpen={isOpenCancelModal}
        onOpenChange={setIsOpenCancelModal}
        title="Cancel Receipt"
        description={`Are you sure you want to cancel this receipt for plot ${selectedReceiptPlot}?`}
        // confirmButtonLabel="Cancel Receipt"
        // confirmButtonColor="red"
      >
        <form>
          <div className="flex flex-col">
            <label htmlFor="reason">Reason *</label>
            <textarea
              name="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary "
              rows={4}
            ></textarea>

            {cancelling ? (
              <span className="flex items-center">
                {" "}
                <Loader2 className="animate animate-spin" /> Cancelling
              </span>
            ) : (
              <div className="flex justify-end items-center gap-3 my-4">
                <PrimaryButton
                  onClick={() => setIsOpenCancelModal(false)}
                  variant="outline"
                >
                  Cancel
                </PrimaryButton>
                <PrimaryButton
                  type="button"
                  variant="default"
                  onClick={handleCancelReciept}
                >
                  Confirm
                </PrimaryButton>
              </div>
            )}
          </div>
        </form>
      </BaseModal>
    </div>
  );
};

export default MinReceiptsModal;
