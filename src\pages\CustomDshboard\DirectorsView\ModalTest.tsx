import React, { useState } from 'react';
import { Button } from '@/components/ui/button';

// Simple test component to verify modal functionality
const ModalTest: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Modal Test</h1>
      
      <Button 
        onClick={() => setIsOpen(true)}
        className="mb-4"
      >
        Open Test Modal
      </Button>

      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setIsOpen(false)}
        >
          <div 
            className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="text-xl font-bold mb-4">Test Modal</h2>
            <p className="mb-4">This is a simple test modal to verify functionality.</p>
            <Button onClick={() => setIsOpen(false)}>
              Close
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModalTest;
