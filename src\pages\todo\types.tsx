// Enhanced types for Trello-like functionality
export interface Label {
  id: string;
  name: string;
  color: string;
}

export interface User {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  initials: string;
}

export interface Attachment {
  id: string;
  name: string;
  url: string;
  type: 'image' | 'document' | 'link';
  size?: number;
  uploadedAt: Date;
}

export interface Comment {
  id: string;
  text: string;
  author: User;
  createdAt: Date;
  updatedAt?: Date;
}

export interface Checklist {
  id: string;
  title: string;
  items: ChecklistItem[];
}

export interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
  assignee?: User;
}

export interface CardType {
  id: string;
  title: string;
  description?: string;
  column: string;
  position: number;

  // Assignees and collaboration
  assignees: User[];

  // Organization and categorization
  labels: Label[];

  // Timing and deadlines
  dueDate?: Date;
  startDate?: Date;

  // Rich content
  attachments: Attachment[];
  comments: Comment[];
  checklists: Checklist[];

  // Metadata
  createdAt: Date;
  updatedAt: Date;
  createdBy: User;

  // Visual and priority
  coverImage?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';

  // Status tracking
  isArchived?: boolean;
  estimatedHours?: number;
  actualHours?: number;
}

export interface ColumnType {
  id: string;
  title: string;
  position: number;
  color: string;
  limit?: number; // WIP limit
  isCollapsed?: boolean;
}

export interface BoardType {
  id: string;
  title: string;
  description?: string;
  columns: ColumnType[];
  cards: CardType[];
  members: User[];
  labels: Label[];
  background?: string;
  isPrivate: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Activity tracking
export interface Activity {
  id: string;
  type: 'card_created' | 'card_moved' | 'card_updated' | 'comment_added' | 'member_added';
  description: string;
  user: User;
  cardId?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface UserMinimal {
  id: number;
  employee_no: string;
  fullnames: string;
  email: string;
}

export interface CustomerMinimal {
  customer_no: string;
  customer_name: string;
  phone?: string;
}

export interface ProspectMinimal {
  id: number;
  name: string;
  phone?: string;
  email?: string;
}

export interface LeadFileMinimal {
  lead_file_no: string;
  customer_name: string;
  plot_no?: string;
}

export interface ToDo {
  todo_id: string; // String ID as per model
  title: string;
  description?: string;
  due_date?: string;
  due_time?: string;
  status: ValidStatus;
  priority?: string;
  set_reminder?: boolean;
  reminder_time?: string;
  client_type?: string;
  created_at?: string;
  created_by?: any;
  customer?: any;
  prospect?: any;
  sale?: any;
  assigned_to?: any; // From API response
}
