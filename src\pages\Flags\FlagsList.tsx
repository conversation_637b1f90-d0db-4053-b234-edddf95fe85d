import { useState, useEffect } from 'react';
import { Screen } from '@/app-components/layout/screen';
import { Flag, Search, Users, Building, TrendingUp } from 'lucide-react';

import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Input } from '@/components/ui/input';
import { motion } from 'framer-motion';
import { useGetFlagsQuery } from '@/redux/slices/flagsApiSlice';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import clsx from 'clsx';

interface FlagType {
  flag_id: string;
  flag_reason: 'Follow-up Required' | 'Payment Issue' | 'Customer Complaint' | 'Documentation Missing' | 'Quality Issue' | 'Process Violation' | 'Other' | null;
  description: string;
  is_resolved: boolean;
  resolution_date: string | null;
  resolution_notes: string | null;
  follow_up_required: boolean;
  follow_up_date: string | null;
  set_reminder: boolean;
  reminder_time: string | null;
  client_type: 'Prospect' | 'Customer' | 'Sale';
  created_at: string;
  created_by: string | null;
  customer: string | null;
  prospect: number | null;
  sale: string | null;
}

const getStatusColor = (isResolved: boolean) => {
  return isResolved
    ? 'bg-green-100 text-green-700 border-green-200'
    : 'bg-red-100 text-red-700 border-red-200';
};

const getReasonColor = (reason: string | null) => {
  switch (reason) {
    case 'Follow-up Required':
      return 'bg-blue-100 text-blue-700 border-blue-200';
    case 'Payment Issue':
      return 'bg-red-100 text-red-700 border-red-200';
    case 'Customer Complaint':
      return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'Documentation Missing':
      return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    case 'Quality Issue':
      return 'bg-purple-100 text-purple-700 border-purple-200';
    case 'Process Violation':
      return 'bg-pink-100 text-pink-700 border-pink-200';
    case 'Other':
      return 'bg-gray-100 text-gray-700 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};

const ENTITY_TABS = [
  {
    id: 'all',
    label: 'All Flags',
    description: 'All flags across the system',
    filter: (flag: FlagType) => true,
  },
  {
    id: 'customer',
    label: 'Customer',
    description: 'Flags related to customers',
    filter: (flag: FlagType) => flag.client_type === 'Customer',
  },
  {
    id: 'sale',
    label: 'Sales',
    description: 'Flags related to sales',
    filter: (flag: FlagType) => flag.client_type === 'Sale',
  },
  {
    id: 'prospect',
    label: 'Prospects',
    description: 'Flags related to prospects',
    filter: (flag: FlagType) => flag.client_type === 'Prospect',
  },
];

export default function FlagList() {
  console.log('🚀 FlagList Component Mounted/Re-rendered');

  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // Fetch all flags without query parameters (except pagination)
  const queryParams = {
    page: currentPage,
    page_size: 10,
  };
  console.log('🔍 Query Parameters:', queryParams);

  const { data: apiResponse, isLoading, isError, error, refetch } = useGetFlagsQuery(queryParams);

  // FIXED: Extract data directly from apiResponse, not from nested data property
  const allFlags: FlagType[] = apiResponse?.results || [];
  const totalPages = apiResponse?.num_pages || 1;
  const totalFlags = apiResponse?.total_data || 0;

  // Apply client-side filtering based on active tab
  const currentTabConfig = ENTITY_TABS.find((tab) => tab.id === activeTab) || ENTITY_TABS[0];
  let flags = allFlags.filter(currentTabConfig.filter);

  // Apply search filter client-side
  if (searchTerm) {
    flags = flags.filter((flag) =>
      flag.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }

  useEffect(() => {
    console.log('📊 Full API Response:', apiResponse);
    console.log('📋 All Flags Data:', allFlags);
    console.log('📋 Filtered Flags Data:', flags);
    if (isLoading) console.log('⏳ Fetching flags...');
    if (isError) console.error('🚨 Error fetching flags:', error);
  }, [apiResponse, allFlags, flags, isLoading, isError, error]);

  console.log('📈 FlagList Pagination Info:', {
    totalPages,
    totalFlags,
    currentPage,
    flagsCount: flags.length,
  });

  return (
    <Screen>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-950 dark:via-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-8">
          {/* Header Section */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
            className="relative overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-pink-600/5 rounded-3xl"></div>
            <div className="absolute inset-0 bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl rounded-3xl border border-white/20 dark:border-gray-700/50"></div>

            <div className="relative p-6 sm:p-8 lg:p-10">
              <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between gap-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
                      <Flag className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
                        Flags Dashboard
                      </h1>
                      <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">
                        Monitor and manage system flags across all entities
                      </p>
                    </div>
                  </div>
                </div>

                <div className="w-full xl:w-96">
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
                    <div className="relative">
                      <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
                      <Input
                        placeholder="Search flags by description..."
                        value={searchTerm}
                        onChange={(e) => {
                          setSearchTerm(e.target.value);
                          setCurrentPage(1);
                        }}
                        className="pl-12 pr-4 py-3 w-full rounded-2xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg focus:shadow-xl focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 text-gray-900 dark:text-white placeholder:text-gray-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Analytics Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            
          </motion.div>

          {/* Tabbed Interface */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative"
          >
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-12">
              {/* Normal Tabs with visible borders */}
              <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 bg-gray-50 dark:bg-gray-900 rounded-lg p-2 gap-2">
                {ENTITY_TABS.map((tab) => (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="py-3 px-4 rounded-md border-2 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 data-[state=active]:border-green-500 data-[state=active]:bg-green-50 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/20 dark:data-[state=active]:text-green-400 hover:border-green-400 hover:bg-green-50/50 transition-all font-medium text-sm"
                  >
                    <div className="text-center">
                      <div className="font-semibold">{tab.label}</div>
                      <div className="text-xs opacity-75 hidden md:block mt-1">
                        {tab.description}
                      </div>
                    </div>
                  </TabsTrigger>
                ))}
              </TabsList>

              {ENTITY_TABS.map((tab) => (
                <TabsContent key={tab.id} value={tab.id} className="space-y-6">
                  {isLoading ? (
                    <div className="flex items-center justify-center py-20">
                      <div className="text-center space-y-4">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="text-gray-600 dark:text-gray-400 text-lg">Loading flags...</p>
                      </div>
                    </div>
                  ) : isError ? (
                    <div className="text-center py-20">
                      <div className="bg-red-50 dark:bg-red-900/20 rounded-2xl p-8 max-w-md mx-auto">
                        <Flag className="h-12 w-12 text-red-500 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-red-700 dark:text-red-400 mb-2">
                          Error Loading Flags
                        </h3>
                        <p className="text-red-600 dark:text-red-400 text-sm">
                          {error ? (error as any).data?.message || JSON.stringify(error) : 'Unknown error occurred'}
                        </p>
                        <button
                          onClick={() => refetch()}
                          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg"
                        >
                          Retry
                        </button>
                      </div>
                    </div>
                  ) : flags.length === 0 ? (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                      className="text-center py-16 sm:py-20"
                    >
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/50 to-pink-50/50 dark:from-blue-900/10 dark:via-purple-900/10 dark:to-pink-900/10 rounded-3xl blur-3xl"></div>
                        <div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-3xl p-8 sm:p-12 max-w-lg mx-auto border border-white/20 dark:border-gray-700/50 shadow-2xl">
                          <div className="p-4 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-2xl w-fit mx-auto mb-6">
                            <Flag className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400 dark:text-gray-500" />
                          </div>
                          <h3 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-gray-100 mb-3">
                            No {tab.label} Flags
                          </h3>
                          <p className="text-gray-600 dark:text-gray-400 text-sm sm:text-base leading-relaxed max-w-sm mx-auto">
                            {tab.description}. Try switching to the "All Flags" tab or checking your search query.
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 sm:gap-6"
                    >
                      {flags.map((flag, index) => (
                        <motion.div
                          key={flag.flag_id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.5, delay: index * 0.05 }}
                        >
                          <Card className="group relative overflow-hidden rounded-xl sm:rounded-2xl border-0 shadow-lg hover:shadow-2xl transition-all duration-300 bg-gradient-to-br from-white via-gray-50 to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 hover:scale-[1.01] sm:hover:scale-[1.02] h-full">
                            <div
                              className={clsx(
                                'absolute top-0 left-0 w-full h-1',
                                flag.is_resolved ? 'bg-green-500' : 'bg-red-500'
                              )}
                            />
                            <CardHeader className="pb-2 sm:pb-3 p-4 sm:p-6">
                              <div className="flex items-start justify-between gap-3">
                                <div className="flex items-center gap-2 sm:gap-3 flex-1 min-w-0">
                                  <div className="p-1.5 sm:p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex-shrink-0">
                                    <Flag className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600 dark:text-blue-400" />
                                  </div>
                                  <div className="min-w-0 flex-1">
                                    <CardTitle className="text-sm sm:text-base lg:text-lg font-bold text-gray-900 dark:text-gray-100 truncate leading-tight">
                                      {flag.description.length > 10
                                        ? flag.description.substring(0, 30) + (flag.description.length > 30 ? '...' : '')
                                        : `Flag ${flag.flag_id}`}
                                    </CardTitle>
                                    <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-0.5">
                                      {flag.created_at && !isNaN(new Date(flag.created_at).getTime())
                                        ? new Date(flag.created_at).toLocaleDateString('en-US', {
                                            year: 'numeric',
                                            month: 'short',
                                            day: 'numeric',
                                          })
                                        : 'No date'}
                                    </p>
                                  </div>
                                </div>
                                <div className="flex flex-col gap-1.5 sm:gap-2 items-end flex-shrink-0">
                                  <Badge
                                    className={clsx(
                                      'text-xs font-semibold border px-2 py-0.5',
                                      getStatusColor(flag.is_resolved)
                                    )}
                                  >
                                    {flag.is_resolved ? 'Resolved' : 'Active'}
                                  </Badge>
                                  <Badge
                                    className={clsx(
                                      'text-xs font-semibold border px-2 py-0.5',
                                      getReasonColor(flag.flag_reason)
                                    )}
                                  >
                                    {flag.flag_reason || 'Unspecified'}
                                  </Badge>
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent className="pb-3 sm:pb-4 px-4 sm:px-6 flex-1">
                              <p className="text-gray-700 dark:text-gray-300 text-xs sm:text-sm mb-3 sm:mb-4 line-clamp-2 sm:line-clamp-3 leading-relaxed">
                                {flag.description}
                              </p>
                              <div className="space-y-2 sm:space-y-3">
                                {(flag.customer || flag.prospect || flag.sale) && (
                                  <div className="space-y-1.5 sm:space-y-2">
                                    {flag.customer && (
                                      <div className="flex items-center gap-1.5 sm:gap-2 flex-wrap">
                                        <span className="text-xs font-medium text-gray-500 dark:text-gray-400 flex-shrink-0">
                                          Customer:
                                        </span>
                                        <Badge variant="outline" className="text-xs px-2 py-0.5 truncate max-w-32">
                                          {flag.customer}
                                        </Badge>
                                      </div>
                                    )}
                                    {flag.prospect && (
                                      <div className="flex items-center gap-1.5 sm:gap-2 flex-wrap">
                                        <span className="text-xs font-medium text-gray-500 dark:text-gray-400 flex-shrink-0">
                                          Prospect:
                                        </span>
                                        <Badge variant="outline" className="text-xs px-2 py-0.5 truncate max-w-32">
                                          {flag.prospect}
                                        </Badge>
                                      </div>
                                    )}
                                    {flag.sale && (
                                      <div className="flex items-center gap-1.5 sm:gap-2 flex-wrap">
                                        <span className="text-xs font-medium text-gray-500 dark:text-gray-400 flex-shrink-0">
                                          Sale:
                                        </span>
                                        <Badge variant="outline" className="text-xs px-2 py-0.5 truncate max-w-32">
                                          {flag.sale}
                                        </Badge>
                                      </div>
                                    )}
                                  </div>
                                )}
                                <div className="flex items-center gap-1.5 sm:gap-2 flex-wrap">
                                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400 flex-shrink-0">
                                    Client Type:
                                  </span>
                                  <Badge
                                    className="text-xs bg-blue-100 text-blue-700 border-blue-200 px-2 py-0.5 truncate max-w-32"
                                  >
                                    {flag.client_type}
                                  </Badge>
                                </div>
                                {flag.follow_up_required && (
                                  <div className="flex items-center gap-1.5 sm:gap-2 flex-wrap">
                                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 flex-shrink-0">
                                      Follow-up:
                                    </span>
                                    <Badge variant="outline" className="text-xs px-2 py-0.5 truncate max-w-32">
                                      Due{' '}
                                      {flag.follow_up_date && !isNaN(new Date(flag.follow_up_date).getTime())
                                        ? new Date(flag.follow_up_date).toLocaleDateString('en-US', {
                                            month: 'short',
                                            day: 'numeric',
                                          })
                                        : 'Not set'}
                                    </Badge>
                                  </div>
                                )}
                                {flag.set_reminder && flag.reminder_time && (
                                  <div className="flex items-center gap-1.5 sm:gap-2 flex-wrap">
                                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 flex-shrink-0">
                                      Reminder:
                                    </span>
                                    <Badge variant="outline" className="text-xs px-2 py-0.5 truncate max-w-32">
                                      {flag.reminder_time && !isNaN(new Date(flag.reminder_time).getTime())
                                        ? new Date(flag.reminder_time).toLocaleString('en-US', {
                                            month: 'short',
                                            day: 'numeric',
                                            hour: 'numeric',
                                            minute: 'numeric',
                                          })
                                        : 'Invalid date'}
                                    </Badge>
                                  </div>
                                )}
                              </div>
                            </CardContent>
                            <CardFooter className="pt-0 px-4 sm:px-6 pb-4 sm:pb-6">
                              <div className="w-full flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 gap-2">
                                <span className="truncate">By {flag.created_by || 'Unknown'}</span>
                                {flag.created_at && (
                                  <span className="flex-shrink-0 text-xs">
                                    {new Date(flag.created_at).toLocaleDateString('en-US', {
                                      month: 'short',
                                      day: 'numeric',
                                    })}
                                  </span>
                                )}
                              </div>
                            </CardFooter>
                            <div className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <div className="absolute -inset-1 rounded-2xl bg-gradient-to-br from-blue-400/5 via-purple-400/5 to-pink-400/5 blur-xl" />
                            </div>
                          </Card>
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </TabsContent>
              ))}
            </Tabs>
          </motion.div>

          {totalPages > 1 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="flex justify-center px-4"
            >
              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 dark:border-gray-700/50 p-2 sm:p-3">
                <Pagination>
                  <PaginationContent className="gap-1 sm:gap-2">
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                        aria-disabled={currentPage === 1}
                        className={clsx(
                          'transition-all duration-200 text-xs sm:text-sm px-2 sm:px-3 py-1.5 sm:py-2',
                          currentPage === 1
                            ? 'pointer-events-none opacity-50'
                            : 'hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600'
                        )}
                      />
                    </PaginationItem>
                    <PaginationItem>
                      <div className="px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 text-xs sm:text-sm font-medium border border-blue-200/50 dark:border-blue-700/50">
                        <span className="hidden sm:inline">Page </span>
                        {currentPage} of {totalPages}
                      </div>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                        aria-disabled={currentPage === totalPages}
                        className={clsx(
                          'transition-all duration-200 text-xs sm:text-sm px-2 sm:px-3 py-1.5 sm:py-2',
                          currentPage === totalPages
                            ? 'pointer-events-none opacity-50'
                            : 'hover:bg-blue-50 dark:hover:bg-gray-700 hover:text-blue-600'
                        )}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </Screen>
  );
}