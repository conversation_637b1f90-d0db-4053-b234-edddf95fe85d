import React, { useState } from "react";
import { motion } from "framer-motion";
import { Screen } from "@/app-components/layout/screen";
import { CardDetailModal } from "./CardDetailModal";
import { AddCardModal } from "./AddCardModal";
import {
  Plus,
  
  MoreHorizontal,
  Star,

  Calendar,

  MessageSquare,
  Paperclip,
  CheckSquare,
  Eye,
  Edit3,
  AlertCircle,
  Flag,
  Trash2,
  Zap,
  Target,
  TrendingUp,
  Activity,
  Bell,
  User,
  ChevronDown,
  Sparkles,
  BarChart3,
  Layout,
  Layers,
} from "lucide-react";
import { useGetTodoQuery, useCreateTodoMutation, useUpdateTodoMutation, useDeleteTodoMutation } from "@/redux/slices/todoApiSlice";

// Define valid statuses for type safety
type ValidStatus = "PENDING" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";

// API Todo interface matching the model
interface ApiTodo {
  todo_id: string; // String ID as per model
  title: string;
  description?: string;
  due_date?: string;
  due_time?: string;
  status: ValidStatus;
  priority?: string;
  set_reminder?: boolean;
  reminder_time?: string;
  client_type?: string;
  created_at?: string;
  created_by?: any;
  customer?: any;
  prospect?: any;
  sale?: any;
  assigned_to?: any;
}

// Modern card interface
interface ModernCard {
  id: string; // This will use todo_id
  title: string;
  description?: string;
  column: ValidStatus;
  assignee?: string;
  labels?: { id: string; name: string; color: string }[];
  dueDate?: Date;
  priority?: "low" | "medium" | "high" | "urgent";
  attachments?: number;
  comments?: number;
  checklist?: { completed: number; total: number };
}

// Sample data updated to use valid statuses
const SAMPLE_CARDS: ModernCard[] = [
  {
    id: "sample-1",
    title: "Design new landing page",
    description: "Create a modern, responsive landing page for the new product launch",
    column: "PENDING",
    assignee: "John Doe",
    labels: [
      { id: "1", name: "Design", color: "#10B981" },
      { id: "2", name: "High Priority", color: "#EF4444" },
    ],
    dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    priority: "high",
    attachments: 3,
    comments: 2,
    checklist: { completed: 2, total: 5 },
  },
  {
    id: "sample-2",
    title: "API Integration",
    description: "Integrate payment gateway API",
    column: "IN_PROGRESS",
    assignee: "Jane Smith",
    labels: [{ id: "3", name: "Backend", color: "#3B82F6" }],
    priority: "urgent",
    comments: 1,
    checklist: { completed: 3, total: 4 },
  },
  {
    id: "sample-3",
    title: "User Testing",
    description: "Conduct user testing sessions",
    column: "COMPLETED",
    assignee: "Mike Johnson",
    labels: [{ id: "4", name: "Research", color: "#8B5CF6" }],
    priority: "medium",
    attachments: 1,
    checklist: { completed: 5, total: 5 },
  },
];

// Updated COLUMNS to use valid statuses
const COLUMNS = [
  { id: "PENDING", title: "Pending", color: "from-gray-400 to-gray-500" },
  { id: "IN_PROGRESS", title: "In Progress", color: "from-blue-400 to-indigo-500" },
  { id: "COMPLETED", title: "Completed", color: "from-yellow-400 to-orange-500" },
  { id: "CANCELLED", title: "Cancelled", color: "from-green-400 to-emerald-500" },
];

const ModernKanbanCard: React.FC<{
  card: ModernCard;
  onCardClick: (card: ModernCard) => void;
  onDragStart: (e: React.DragEvent, card: ModernCard) => void;
  onDeleteCard?: (cardId: string) => void;
  onToggleSelection?: (cardId: string) => void;
  isSelected?: boolean;
  isUpdatingStatus?: boolean;
}> = ({ card, onCardClick, onDragStart, onDeleteCard, onToggleSelection, isSelected = false, isUpdatingStatus = false }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const getPriorityColor = (priority?: string) => {
    switch (priority?.toLowerCase()) {
      case "urgent":
        return "from-red-500 to-pink-500";
      case "high":
        return "from-orange-500 to-red-500";
      case "medium":
        return "from-yellow-500 to-orange-500";
      case "low":
        return "from-green-500 to-emerald-500";
      default:
        return "from-gray-400 to-gray-500";
    }
  };

  const formatDueDate = (date: Date) => {
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return { text: "Overdue", color: "bg-red-500 text-white" };
    if (diffDays === 0) return { text: "Due today", color: "bg-orange-500 text-white" };
    if (diffDays === 1) return { text: "Due tomorrow", color: "bg-yellow-500 text-white" };
    if (diffDays <= 7) return { text: `${diffDays} days`, color: "bg-blue-500 text-white" };
    return { text: date.toLocaleDateString(), color: "bg-gray-500 text-white" };
  };

  const handleDragStart = (e: React.DragEvent) => {
    setIsDragging(true);
    onDragStart(e, card);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleClick = (e: React.MouseEvent) => {
    if (!isDragging) {
      onCardClick(card);
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: isDragging ? 0 : -2 }}
      transition={{ duration: 0.2 }}
      draggable={!isUpdatingStatus}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
      className={`group relative bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden mb-3 ${
        isDragging ? "opacity-50 rotate-3 scale-105 shadow-2xl" : ""
      } ${isUpdatingStatus ? "cursor-not-allowed opacity-60" : "cursor-move"} ${
        isSelected ? "ring-2 ring-blue-500 border-blue-500" : ""
      }`}
    >
      {/* Selection checkbox */}
      {onToggleSelection && (
        <div className="absolute top-2 left-2 z-10">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={(e) => {
              e.stopPropagation();
              onToggleSelection(card.id);
            }}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
          />
        </div>
      )}
      
      {card.priority && (
        <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${getPriorityColor(card.priority)}`}></div>
      )}
      <div className="p-4">
        {card.labels && card.labels.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {card.labels.slice(0, 2).map((label) => (
              <span
                key={label.id}
                className="px-2 py-1 text-xs font-medium rounded-full text-white shadow-sm"
                style={{ backgroundColor: label.color }}
              >
                {label.name}
              </span>
            ))}
            {card.labels.length > 2 && (
              <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-200 text-gray-600">
                +{card.labels.length - 2}
              </span>
            )}
          </div>
        )}
        <h3 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-2 leading-tight">{card.title}</h3>
        {card.description && (
          <p className="text-xs text-gray-600 mb-3 line-clamp-2">{card.description}</p>
        )}
       
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 text-xs text-gray-500">
            {card.checklist && (
              <div className="flex items-center gap-1">
                <CheckSquare className="h-3 w-3" />
                <span className={card.checklist.completed === card.checklist.total ? "text-green-600 font-medium" : ""}>
                  {card.checklist.completed}/{card.checklist.total}
                </span>
              </div>
            )}
            {card.comments && card.comments > 0 && (
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3" />
                <span>{card.comments}</span>
              </div>
            )}
            {card.attachments && card.attachments > 0 && (
              <div className="flex items-center gap-1">
                <Paperclip className="h-3 w-3" />
                <span>{card.attachments}</span>
              </div>
            )}
          </div>
          {card.assignee && card.assignee !== 'Unassigned' && (
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-xs font-semibold">
              {card.assignee.includes('/') 
                ? card.assignee.split('/').pop()?.substring(0, 2).toUpperCase() || "U"
                : card.assignee.split(" ").map((n) => n[0]).join("").substring(0, 2).toUpperCase()}
            </div>
          )}
        </div>
        {isHovered && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute top-2 right-2 flex gap-1"
          >
            <button 
              onClick={(e) => {
                e.stopPropagation();
                onCardClick(card);
              }}
              className="p-1 bg-white rounded-md shadow-md hover:bg-gray-50 transition-colors"
            >
              <Eye className="h-3 w-3 text-gray-600" />
            </button>
            <button 
              onClick={(e) => {
                e.stopPropagation();
                onCardClick(card);
              }}
              className="p-1 bg-white rounded-md shadow-md hover:bg-gray-50 transition-colors"
            >
              <Edit3 className="h-3 w-3 text-gray-600" />
            </button>
            {onDeleteCard && (
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteCard(card.id);
                }}
                className="p-1 bg-white rounded-md shadow-md hover:bg-red-50 transition-colors group"
              >
                <Trash2 className="h-3 w-3 text-gray-600 group-hover:text-red-600" />
              </button>
            )}
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

const ModernColumn: React.FC<{
  column: any;
  cards: ModernCard[];
  onCardClick: (card: ModernCard) => void;
  onAddCard: (columnId: string, columnTitle: string) => void;
  onDragStart: (e: React.DragEvent, card: ModernCard) => void;
  onDrop: (e: React.DragEvent, columnId: string) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragLeave: (e: React.DragEvent) => void;
  onDeleteCard?: (cardId: string) => void;
  onToggleSelection?: (cardId: string) => void;
  selectedCards?: string[];
  isUpdatingStatus?: boolean;
}> = ({ column, cards, onCardClick, onAddCard, onDragStart, onDrop, onDragOver, onDragLeave, onDeleteCard, onToggleSelection, selectedCards = [], isUpdatingStatus = false }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const columnCards = cards.filter((card) => card.column === column.id);

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    onDrop(e, column.id);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
    onDragOver(e);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
      onDragLeave(e);
    }
  };

  return (
    <div className="w-80 shrink-0">
      <div
        className={`bg-white rounded-xl shadow-sm border-2 overflow-hidden transition-all duration-200 ${
          isDragOver ? "border-blue-400 bg-blue-50 shadow-lg transform scale-105" : "border-gray-200"
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg font-bold text-gray-900">{column.title}</h2>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                {columnCards.length}
              </span>
              <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                <MoreHorizontal className="h-4 w-4" />
              </button>
            </div>
          </div>
          <div className={`h-1 w-full bg-gradient-to-r ${column.color} rounded-full`}></div>
          {isDragOver && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-2 p-2 bg-blue-100 border border-blue-300 rounded-lg text-center"
            >
              <span className="text-sm text-blue-700 font-medium">Drop card here</span>
            </motion.div>
          )}
        </div>
        <div className="p-4 max-h-96 overflow-y-auto">
          {columnCards.map((card) => (
            <ModernKanbanCard
              key={card.id}
              card={card}
              onCardClick={onCardClick}
              onDragStart={onDragStart}
              onDeleteCard={onDeleteCard}
              onToggleSelection={onToggleSelection}
              isSelected={selectedCards.includes(card.id)}
              isUpdatingStatus={isUpdatingStatus}
            />
          ))}
          <button
            onClick={() => onAddCard(column.id, column.title)}
            className="w-full p-3 border-2 border-dashed border-gray-300 rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 flex items-center justify-center gap-2 text-gray-500 hover:text-gray-600"
          >
            <Plus className="h-4 w-4" />
            <span className="text-sm font-medium">Add a card</span>
          </button>
        </div>
        <div className="p-4 border-t border-gray-100 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{columnCards.length} cards</span>
            <button className="text-blue-600 hover:text-blue-700 font-medium">View all</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ModernBoard: React.FC = () => {
  const [isAddCardModalOpen, setIsAddCardModalOpen] = useState(false);
  const [selectedColumn, setSelectedColumn] = useState<{ id: string; title: string } | null>(null);
  const [selectedCard, setSelectedCard] = useState<ModernCard | null>(null);
  const [draggedCard, setDraggedCard] = useState<ModernCard | null>(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [selectedCards, setSelectedCards] = useState<string[]>([]);

  // Query todos from API
  const { data: todos, isLoading, error, refetch } = useGetTodoQuery({
    page: 1,
    page_size: 100,
    ordering: "-created_at"
  });
  
  // Mutation hooks
  const [createTodo, { isLoading: isCreatingTodo }] = useCreateTodoMutation();
  const [updateTodo, { isLoading: isUpdatingTodo }] = useUpdateTodoMutation();
  const [deleteTodo, { isLoading: isDeletingTodo }] = useDeleteTodoMutation();

  // Helper function to get priority color
  const getPriorityColor = (priority: string): string => {
    switch (priority?.toLowerCase()) {
      case 'urgent':
        return '#EF4444';
      case 'high':
        return '#F97316';
      case 'medium':
        return '#EAB308';
      case 'low':
        return '#10B981';
      default:
        return '#6B7280';
    }
  };

  // Transform API data to ModernCard format
  const transformTodoToCard = (todo: ApiTodo): ModernCard => {
    try {
      const card: ModernCard = {
        id: todo.todo_id, // Use string ID from API
        title: todo.title || 'Untitled Task',
        description: todo.description || '',
        column: todo.status as ValidStatus,
        assignee: todo.assigned_to 
          ? (typeof todo.assigned_to === 'string' 
              ? todo.assigned_to
              : (todo.assigned_to.fullnames || (todo.assigned_to.first_name && todo.assigned_to.last_name 
                  ? `${todo.assigned_to.first_name} ${todo.assigned_to.last_name}` 
                  : 'Assigned User')))
          : 'Unassigned',
        labels: todo.priority ? [{ 
          id: todo.priority, 
          name: todo.priority.charAt(0).toUpperCase() + todo.priority.slice(1).toLowerCase(), 
          color: getPriorityColor(todo.priority) 
        }] : [],
        dueDate: todo.due_date ? new Date(todo.due_date) : undefined,
        priority: todo.priority?.toLowerCase() as "low" | "medium" | "high" | "urgent",
        attachments: 0,
        comments: 0,
        checklist: undefined,
      };
      
      return card;
    } catch (error) {
      console.error("Error transforming todo to card:", error, todo);
      return {
        id: todo.todo_id || `error-${Math.random().toString(36).substr(2, 9)}`,
        title: todo.title || 'Error loading task',
        description: 'There was an error loading this task',
        column: 'PENDING',
        attachments: 0,
        comments: 0,
      };
    }
  };

  // Transform todos to cards
  const cards: ModernCard[] = React.useMemo(() => {
    const actualTodos = todos?.data?.results || [];
    
    if (!Array.isArray(actualTodos)) {
      console.log("No todos found, using sample data");
      return SAMPLE_CARDS;
    }
    
    const transformedCards = actualTodos.map(transformTodoToCard);
    
    if (transformedCards.length === 0) {
      console.log("No transformed cards, using sample data");
      return SAMPLE_CARDS;
    }
    
    return transformedCards;
  }, [todos]);

  // Handle adding a new card
  const handleAddCard = async (newCardData: any) => {
    try {
      const result = await createTodo({
        ...newCardData,
        status: selectedColumn?.id || "PENDING",
      }).unwrap();
      
      setIsAddCardModalOpen(false);
      setSelectedColumn(null);
      await refetch();
      
    } catch (error: any) {
      const errorMessage = error?.data?.detail || 
                          error?.data?.message || 
                          "Failed to create task. Please try again.";
      alert(errorMessage);
    }
  };

  const handleCardClick = (card: ModernCard) => {
    setSelectedCard(card);
  };

  const openAddCardModal = (columnId: string, columnTitle: string) => {
    setSelectedColumn({ id: columnId, title: columnTitle });
    setIsAddCardModalOpen(true);
  };

  // Handle deleting a todo
  const handleDeleteTodo = async (todoId: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this task?');
    
    if (!confirmed) {
      return;
    }

    try {
      // Use string ID directly - no conversion needed
      await deleteTodo(todoId).unwrap();
      setSelectedCard(null);
      await refetch();
      
    } catch (error: any) {
      const errorMessage = error?.data?.detail || 
                          error?.data?.message || 
                          error?.message || 
                          "Failed to delete task. Please try again.";
      alert(errorMessage);
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedCards.length === 0) return;
    
    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedCards.length} task${selectedCards.length > 1 ? 's' : ''}?`
    );
    
    if (!confirmed) return;

    try {
      const deleteResults = await Promise.allSettled(
        selectedCards.map(async (cardId) => {
          // Use string ID directly
          return deleteTodo(cardId).unwrap();
        })
      );
      
      const successful = deleteResults.filter(result => result.status === 'fulfilled').length;
      const failed = deleteResults.filter(result => result.status === 'rejected').length;
      
      if (successful > 0) {
        console.log(`Successfully deleted ${successful} tasks`);
      }
      
      if (failed > 0) {
        alert(`Successfully deleted ${successful} tasks, but ${failed} failed.`);
      }
      
      setSelectedCards([]);
      await refetch();
      
    } catch (error: any) {
      alert("Failed to delete tasks. Please try again.");
    }
  };

  const toggleCardSelection = (cardId: string) => {
    setSelectedCards(prev => 
      prev.includes(cardId) 
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  const selectAllCards = () => {
    setSelectedCards(cards.map(card => card.id));
  };

  const deselectAllCards = () => {
    setSelectedCards([]);
  };

  const handleDragStart = (e: React.DragEvent, card: ModernCard) => {
    if (isUpdatingStatus) {
      e.preventDefault();
      return;
    }
    
    setDraggedCard(card);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", card.id);
    setTimeout(() => {
      const dragImage = e.target as HTMLElement;
      dragImage.style.opacity = "0.5";
    }, 0);
  };

  const handleDrop = async (e: React.DragEvent, targetColumnId: ValidStatus) => {
    e.preventDefault();
    
    if (!draggedCard || draggedCard.column === targetColumnId) {
      return;
    }

    setIsUpdatingStatus(true);
    
    try {
      const originalTodo = todos?.data?.results.find(t => t.todo_id === draggedCard.id);
      
      if (!originalTodo) {
        throw new Error("Original todo not found");
      }

      await updateTodo({
        todo_id: originalTodo.todo_id, // Use string ID
        status: targetColumnId,
        title: originalTodo.title,
        description: originalTodo.description,
        priority: originalTodo.priority,
        due_date: originalTodo.due_date,
        assigned_to: originalTodo.assigned_to
      }).unwrap();

      await refetch();
      
    } catch (error: any) {
      const errorMessage = error?.data?.detail || 
                          error?.data?.message || 
                          error?.message || 
                          "Failed to move task. Please try again.";
      
      alert(`Failed to move task: ${errorMessage}`);
    } finally {
      setIsUpdatingStatus(false);
      setDraggedCard(null);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDragLeave = (e: React.DragEvent) => {};

  // Loading state
  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="text-lg font-medium text-gray-600">Loading todos...</span>
          </div>
        </div>
      </Screen>
    );
  }

  // Error state
  if (error) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-lg font-medium mb-2">Error loading todos</div>
            <p className="text-gray-600 mb-4">
              {error?.data?.detail || error?.data?.message || "Please try refreshing the page"}
            </p>
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      {/* Mutation loading overlay */}
      {(isCreatingTodo || isUpdatingTodo || isDeletingTodo) && (
        <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 shadow-xl">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="text-gray-700">
                {isCreatingTodo && "Creating task..."}
                {isUpdatingTodo && "Updating task..."}
                {isDeletingTodo && "Deleting task..."}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="sticky top-0 z-20 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-600 shadow-2xl">
        <div className="relative z-10">
          <div className="flex flex-wrap lg:flex-nowrap items-center justify-between px-4 md:px-8 py-6 gap-4">
            <div className="flex items-center gap-4 min-w-0">
              <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm shadow-lg shrink-0">
                <Layout className="h-8 w-8 text-white" />
              </div>
              <div className="min-w-0">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl md:text-3xl font-bold text-white truncate max-w-xs md:max-w-md">
                    Project Board
                  </h1>
                  <button className="p-2 text-white/70 hover:text-yellow-300 transition-colors transform hover:scale-110">
                    <Star className="h-6 w-6" />
                  </button>
                </div>
                <p className="text-green-100 text-base md:text-lg truncate max-w-xs md:max-w-md">
                  Manage your tasks and workflow efficiently • {cards.length} tasks loaded
                </p>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-3 justify-end min-w-0">
              
              <div className="flex flex-wrap items-center gap-2">
                
                <button 
                  onClick={selectedCards.length === cards.length ? deselectAllCards : selectAllCards}
                  className="flex items-center gap-2 px-4 py-3 bg-white/20 hover:bg-white/30 text-white rounded-xl font-semibold transition-all duration-200 backdrop-blur-sm border border-white/30 hover:border-white/50"
                >
                  <CheckSquare className="h-5 w-5" />
                  <span>{selectedCards.length === cards.length ? 'Deselect All' : 'Select All'}</span>
                </button>
                {selectedCards.length > 0 && (
                  <div className="flex items-center gap-2 px-4 py-3 bg-red-500/20 border border-red-300/30 text-white rounded-xl backdrop-blur-sm">
                    <span className="text-sm font-medium">{selectedCards.length} selected</span>
                    <button
                      onClick={deselectAllCards}
                      className="text-xs text-red-200 hover:text-white transition-colors"
                    >
                      Clear
                    </button>
                    <button
                      onClick={handleBulkDelete}
                      disabled={isDeletingTodo}
                      className="flex items-center gap-1 px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors disabled:opacity-50"
                    >
                      <Trash2 className="h-4 w-4" />
                      {isDeletingTodo ? "Deleting..." : "Delete"}
                    </button>
                  </div>
                )}
                <button
                  onClick={() => openAddCardModal("PENDING", "Pending")}
                  disabled={isCreatingTodo}
                  className="flex items-center gap-2 px-6 py-3 bg-white text-green-600 rounded-xl font-semibold hover:bg-green-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:transform-none"
                >
                  <Plus className="h-5 w-5" />
                  <span>{isCreatingTodo ? "Adding..." : "Add Task"}</span>
                  <Sparkles className="h-4 w-4 opacity-70" />
                </button>
              </div>
            </div>
          </div>
          <div className="px-4 md:px-8 pb-6 overflow-x-auto">
            <div className="flex flex-wrap gap-4 items-center justify-between">
              {COLUMNS.map((column, index) => {
                const columnCards = cards.filter((card) => card.column === column.id);
                return (
                  <motion.div
                    key={column.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="flex items-center gap-3 px-4 py-2 bg-white/20 rounded-xl backdrop-blur-sm border border-white/30 min-w-[120px]"
                  >
                    <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${column.color}`}></div>
                    <span className="text-white font-medium truncate">{column.title}</span>
                    <span className="text-white/80 text-sm bg-white/20 px-2 py-1 rounded-full">
                      {columnCards.length}
                    </span>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Board columns */}
      <div className={`flex gap-6 overflow-x-auto p-6 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen transition-all duration-200 ${
        draggedCard ? "bg-gradient-to-br from-blue-100 via-indigo-100 to-purple-100" : ""
      }`}
      >
        {COLUMNS.map((column) => (
          <ModernColumn
            key={column.id}
            column={column}
            cards={cards}
            onCardClick={handleCardClick}
            onAddCard={openAddCardModal}
            onDragStart={handleDragStart}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDeleteCard={handleDeleteTodo}
            onToggleSelection={toggleCardSelection}
            selectedCards={selectedCards}
            isUpdatingStatus={isUpdatingStatus}
          />
        ))}
        <div className="w-80 shrink-0">
          <button className="w-full h-32 border-2 border-dashed border-gray-300 rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 flex flex-col items-center justify-center gap-2 text-gray-500 hover:text-gray-600">
            <Plus className="h-8 w-8" />
            <span className="text-sm font-medium">Add another list</span>
          </button>
        </div>
      </div>
      
      {/* Drag feedback */}
      {draggedCard && (
        <div className="fixed inset-0 pointer-events-none z-50">
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white border-2 border-blue-400 rounded-xl shadow-2xl p-4 max-w-xs"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    <Flag className="h-4 w-4 text-white" />
                  </motion.div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 text-sm">{draggedCard.title}</h3>
                  <p className="text-xs text-blue-600">
                    {isUpdatingStatus ? "Updating status..." : "Moving card..."}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      )}
      
      {/* Modals */}
      {selectedCard && (
        <CardDetailModal
          isOpen={!!selectedCard}
          onClose={() => setSelectedCard(null)}
          card={selectedCard}
          onDelete={handleDeleteTodo}
          onRefetch={refetch}
        />
      )}
      <AddCardModal
        isOpen={isAddCardModalOpen && !!selectedColumn}
        onClose={() => {
          setIsAddCardModalOpen(false);
          setSelectedColumn(null);
        }}
        columnId={selectedColumn?.id || ""}
        columnTitle={selectedColumn?.title || ""}
        onAddCard={handleAddCard}
      />
    </Screen>
  );
};