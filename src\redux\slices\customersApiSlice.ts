import { contentHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";
import { BASE_URL } from '@/config';
import { getCustomerApiParams } from '@/utils/customerPermissions';
import { RootState } from "../store";

export const customersApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getAllCustomers: builder.query({
      query: (params) => {
        // Directly use the params provided by the component
        // This simplifies the query function and makes debugging easier
        console.log('All Customers API final params:', params);
        
        return {
          url: "/customers/all-customers",
          method: "GET",
          params: params,
        };
      },
      transformResponse: (response: any) => {
        console.log('All Customers API Raw Response:', response);
        
        // The API returns data in response.data structure
        const apiData = response?.data || {};
        
        console.log('All Customers API Data:', apiData);
        
        const transformedData = {
          results: apiData.results || [],
          current_page: apiData.current_page || 1,
          last_page: apiData.last_page || 1,
          per_page: apiData.per_page || 20,
          total_data: apiData.total_data || 0,
          links: apiData.links || { next: null, previous: null }
        };
        
        console.log('All Customers API Transformed Data:', transformedData);
        
        return transformedData;
      },
      transformErrorResponse: (error) => {
        return error;
      },
      providesTags: ["Customers"],
    }),

    // New endpoint for permission-less customer search
    searchAllCustomersPermissionLess: builder.query({
      query: (params) => {
        console.log('Search Customers Permission-less API final params:', params);
        
        return {
          url: "/customers/all-customers-permission-less",
          method: "GET",
          params: params,
        };
      },
      transformResponse: (response: any) => {
        console.log('Search Customers Permission-less API Raw Response:', response);
        
        // The API returns data in response.data structure
        const apiData = response?.data || {};
        
        console.log('Search Customers Permission-less API Data:', apiData);
        
        const transformedData = {
          results: apiData.results || [],
          current_page: apiData.current_page || 1,
          last_page: apiData.last_page || 1,
          per_page: apiData.per_page || 20,
          total_data: apiData.total_data || 0,
          links: apiData.links || { next: null, previous: null }
        };
        
        console.log('Search Customers Permission-less API Transformed Data:', transformedData);
        
        return transformedData;
      },
      transformErrorResponse: (error) => {
        console.error('Search Customers Permission-less API Error:', error);
        return error;
      },
      providesTags: ["SearchCustomers"],
    }),

    // Removed getCustomersByCategory endpoint - now using getAllCustomers with category parameter
    
    getCustomerDetails: builder.query({
      query: (id) => ({
        url: `/customers/all-customers/${id}`,
        method: "GET",
      }),
      providesTags: ["Customers"],
    }),
    
    createCustomer: builder.mutation({
      query: (data) => ({
        url: `/customers`,  
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Customers"],
    }),
    
    updateCustomer: builder.mutation({
      query: ({ id, data }) => ({
        url: `/customers/${id}`,  
        method: "PUT",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Customers"],
    }),
    
    // Fixed to use lead-files endpoint for customer sales
    getCustomerSales: builder.query({
      query: (params) => ({
        url: "/lead-files/",
        method: "GET",
        params: params,
      }),
      transformResponse: (response) => {
        console.log("Lead Files API Response:", response);
        
        // Extract the data object and transform it to match expected structure
        const apiData = response?.data || {};
        
        return {
          results: apiData.results || [],
          count: apiData.total_data || 0,
          next: apiData.current_page < apiData.last_page ? `page=${apiData.current_page + 1}` : null,
          previous: apiData.current_page > 1 ? `page=${apiData.current_page - 1}` : null,
          current_page: apiData.current_page || 1,
          last_page: apiData.last_page || 1,
          per_page: apiData.per_page || 10,
          total_data: apiData.total_data || 0
        };
      },
      transformErrorResponse: (error) => {
        console.error("Lead Files API Error:", error);
        return error;
      },
      providesTags: ["CustomerSales"],
    }),

    // Fixed endpoint for customer installments using lead file numbers
    getCustomerInstallments: builder.query({
      query: (params) => ({
        url: "/installment-schedules/",
        method: "GET",
        params: params,
      }),
      transformResponse: (response) => {
        console.log("Installments API Response:", response);
        
        // Extract the data object and transform it to match expected structure
        const apiData = response?.data || {};
        
        return {
          results: apiData.results || [],
          count: apiData.total_data || 0,
          next: apiData.current_page < apiData.last_page ? `page=${apiData.current_page + 1}` : null,
          previous: apiData.current_page > 1 ? `page=${apiData.current_page - 1}` : null,
          current_page: apiData.current_page || 1,
          last_page: apiData.last_page || 1,
          per_page: apiData.per_page || 10,
          total_data: apiData.total_data || 0
        };
      },
      transformErrorResponse: (error) => {
        console.error("Installments API Error:", error);
        return error;
      },
      providesTags: ["CustomerInstallments"],
    }),

    // Fixed endpoint for customer site visits
    getCustomerSiteVisits: builder.query({
      query: (params) => ({
        url: "/logistics/site-visits",
        method: "GET",
        params: params,
      }),
      transformResponse: (response) => {
        console.log("Site Visits API Response:", response);

        // Extract the data object and transform it to match expected structure
        const apiData = response?.data || {};

        return {
          results: apiData.results || [],
          count: apiData.total_data || 0,
          next: apiData.current_page < apiData.last_page ? `page=${apiData.current_page + 1}` : null,
          previous: apiData.current_page > 1 ? `page=${apiData.current_page - 1}` : null,
          current_page: apiData.current_page || 1,
          last_page: apiData.last_page || 1,
          per_page: apiData.per_page || 10,
          total_data: apiData.total_data || 0
        };
      },
      transformErrorResponse: (error) => {
        console.error("Site Visits API Error:", error);
        return error;
      },
      providesTags: ["CustomerSiteVisits"],
    }),

    // Endpoint for customer bookings
    getCustomerBookings: builder.query({
      query: (params) => ({
        url: "/all-bookings/",
        method: "GET",
        params: params,
      }),
      transformResponse: (response) => {
        console.log("Customer Bookings API Response:", response);

        // Transform the response to match the expected structure
        return {
          results: response.results || [],
          count: response.count || 0,
          current_page: response.current_page || 1,
          num_pages: response.num_pages || 1,
          total_data: response.count || 0,
          per_page: Math.ceil((response.count || 0) / (response.num_pages || 1)),
          next: response.current_page < response.num_pages ? `page=${response.current_page + 1}` : null,
          previous: response.current_page > 1 ? `page=${response.current_page - 1}` : null,
          title: response.Title || "Customer Bookings"
        };
      },
      transformErrorResponse: (error) => {
        console.error("Customer Bookings API Error:", error);
        return error;
      },
      providesTags: ["CustomerBookings"],
    }),
  }),
});

export const {
  useGetAllCustomersQuery,
  useSearchAllCustomersPermissionLessQuery, // New hook for permission-less search
  useGetCustomerDetailsQuery,
  useCreateCustomerMutation,
  useUpdateCustomerMutation,
  useGetCustomerSalesQuery,
  useGetCustomerInstallmentsQuery,
  useGetCustomerSiteVisitsQuery,
  useGetCustomerBookingsQuery,
} = customersApiSlice;