import { Screen } from "@/app-components/layout/screen";
import {
  Pin,
  Lock,
  Clock,
  FileText,
  UserCheck,
  Target,
  TrendingUp,
  Activity,
  BarChart3,
  CheckCircle,
  AlertCircle,
  User,
  Building2,
} from "lucide-react";
import { useState, useEffect } from "react";
import ViewNoteModal from "../../components/servicesdashboard/Notes/VieNotes";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useLazyGetNotesQuery } from "@/redux/slices/notesApiSlice";

// Define RootState for Redux
interface RootState {
  auth: {
    userInfo: {
      id: string;
    } | null;
  };
}

// Utility to compute change and changeLabel
const getTimeDiff = (date: string): { change: string; changeLabel: string } => {
  const now = new Date();
  const updated = new Date(date);
  const diffMs = now.getTime() - updated.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffHours < 1) {
    return { change: "0h", changeLabel: "Just updated" };
  } else if (diffHours < 24) {
    return {
      change: `-${diffHours}h`,
      changeLabel: `Updated ${diffHours} hours ago`,
    };
  } else {
    return {
      change: `+${diffDays}d`,
      changeLabel: `Updated ${diffDays} days ago`,
    };
  }
};

type Note = {
  id: string;
  title: string;
  note_type: string;
  content: string;
  is_private: boolean;
  is_pinned: boolean;
  client_status: string;
  entity_type: string;
  entity_name: string;
  created_at: string;
  updated_at: string;
  change: string;
  changeLabel: string;
  created_by?: string;
};

// Note type enum values from API
const NOTE_TYPES = [
  { value: "all", label: "All Types", icon: FileText, color: "gray" },
  { value: "General", label: "General", icon: FileText, color: "blue" },
  { value: "Important", label: "Important", icon: AlertCircle, color: "red" },
  { value: "Reminder", label: "Reminder", icon: Clock, color: "yellow" },
  { value: "Follow-up", label: "Follow-up", icon: CheckCircle, color: "green" },
  { value: "Internal", label: "Internal", icon: User, color: "purple" },
  {
    value: "Customer Facing",
    label: "Customer Facing",
    icon: Building2,
    color: "indigo",
  },
] as const;

function ViewNotes() {
  const [activeCategory, setActiveCategory] = useState<
    "Customer" | "Lead File" | "Prospect"
  >("Customer");
  const [hoveredNote, setHoveredNote] = useState<string | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [selectedNoteType, setSelectedNoteType] = useState<string>("all");

  // Fetch notes for each category using client_status
  const [triggerCustomer, { data: customerNotes, isLoading: customerLoading }] =
    useLazyGetNotesQuery();
  const [triggerSales, { data: salesNotes, isLoading: salesLoading }] =
    useLazyGetNotesQuery();
  const [
    triggerProspects,
    { data: prospectsNotes, isLoading: prospectsLoading },
  ] = useLazyGetNotesQuery();

  // Fetch notes for each category on component mount and when note type filter changes
  useEffect(() => {
    const filters = {
      client_type: "Customer",
      ...(selectedNoteType && selectedNoteType !== "all"
        ? { note_type: selectedNoteType }
        : {}),
    };
    triggerCustomer(filters);
  }, [triggerCustomer, selectedNoteType]);

  useEffect(() => {
    const filters = {
      client_type: "Lead File",
      ...(selectedNoteType && selectedNoteType !== "all"
        ? { note_type: selectedNoteType }
        : {}),
    };
    triggerSales(filters);
  }, [triggerSales, selectedNoteType]);

  useEffect(() => {
    const filters = {
      client_type: "Prospect",
      ...(selectedNoteType && selectedNoteType !== "all"
        ? { note_type: selectedNoteType }
        : {}),
    };
    triggerProspects(filters);
  }, [triggerProspects, selectedNoteType]);

  // Log API responses for each category
  useEffect(() => {
    if (customerNotes) {
      console.log("Customer Notes API Response:", customerNotes);
    }
  }, [customerNotes]);

  useEffect(() => {
    if (salesNotes) {
      console.log("Sales Notes API Response:", salesNotes);
    }
  }, [salesNotes]);

  useEffect(() => {
    if (prospectsNotes) {
      console.log("Prospects Notes API Response:", prospectsNotes);
    }
  }, [prospectsNotes]);

  // Transform notes data for each category
  const transformNotes = (data: any): Note[] => {
    return data?.results
      ? data.results.map((note: any) => {
          const { change, changeLabel } = getTimeDiff(
            note.updated_at || note.created_at
          );

          let entityName = "";
          if (note.customer) {
            entityName = note.customer;
          } else if (note.prospect) {
            entityName = note.prospect;
          } else if (note.sale) {
            entityName = note.sale;
          }

          return {
            id: note.note_id,
            title: note.title,
            note_type: note.note_type || "General",
            content: note.content || "",
            is_private: note.is_private || false,
            is_pinned: note.is_pinned || false,
            client_status: note.client_type || "",
            entity_type: note.client_type || "",
            entity_name: entityName,
            created_at: note.created_at || "",
            updated_at: note.updated_at || note.created_at || "",
            created_by: note.created_by || "",
            change,
            changeLabel,
          };
        })
      : [];
  };

  const customerNotesData = transformNotes(customerNotes);
  const salesNotesData = transformNotes(salesNotes);
  const prospectsNotesData = transformNotes(prospectsNotes);

  // Calculate stats for each category
  const getStats = (notes: Note[]) => ({
    total: notes.length,
    private: notes.filter((n) => n.is_private).length,
    pinned: notes.filter((n) => n.is_pinned).length,
    recent: notes.filter((n) => {
      const daysDiff = Math.floor(
        (new Date().getTime() - new Date(n.created_at).getTime()) /
          (1000 * 60 * 60 * 24)
      );
      return daysDiff <= 7;
    }).length,
    byType: {
      general: notes.filter((n) => n.note_type === "General").length,
      important: notes.filter((n) => n.note_type === "Important").length,
      reminder: notes.filter((n) => n.note_type === "Reminder").length,
      followUp: notes.filter((n) => n.note_type === "Follow-up").length,
      internal: notes.filter((n) => n.note_type === "Internal").length,
      customerFacing: notes.filter((n) => n.note_type === "Customer Facing")
        .length,
    },
  });

  const customerStats = getStats(customerNotesData);
  const salesStats = getStats(salesNotesData);
  const prospectsStats = getStats(prospectsNotesData);

  const handleViewNote = (note: Note) => {
    setSelectedNote(note);
    setIsViewModalOpen(true);
  };

  const handleNoteUpdate = () => {
    setIsViewModalOpen(false);
    const customerFilters = {
      client_type: "Customer",
      ...(selectedNoteType && selectedNoteType !== "all"
        ? { note_type: selectedNoteType }
        : {}),
    };
    const salesFilters = {
      client_type: "Lead File",
      ...(selectedNoteType && selectedNoteType !== "all"
        ? { note_type: selectedNoteType }
        : {}),
    };
    const prospectsFilters = {
      client_type: "Prospect",
      ...(selectedNoteType && selectedNoteType !== "all"
        ? { note_type: selectedNoteType }
        : {}),
    };

    triggerCustomer(customerFilters);
    triggerSales(salesFilters);
    triggerProspects(prospectsFilters);
  };

  // Helper function to render note cards
  const renderNoteCard = (note: Note, index: number) => {
    const getNoteTypeIcon = (type: string) => {
      switch (type) {
        case "General": return FileText;
        case "Important": return AlertCircle;
        case "Reminder": return Clock;
        case "Follow-up": return CheckCircle;
        case "Internal": return User;
        case "Customer Facing": return Building2;
        default: return FileText;
      }
    };

    const getNoteTypeColor = (type: string) => {
      switch (type) {
        case "General": return { bg: "bg-blue-50", text: "text-blue-600", border: "border-blue-100" };
        case "Important": return { bg: "bg-red-50", text: "text-red-600", border: "border-red-100" };
        case "Reminder": return { bg: "bg-yellow-50", text: "text-yellow-600", border: "border-yellow-100" };
        case "Follow-up": return { bg: "bg-green-50", text: "text-green-600", border: "border-green-100" };
        case "Internal": return { bg: "bg-purple-50", text: "text-purple-600", border: "border-purple-100" };
        case "Customer Facing": return { bg: "bg-indigo-50", text: "text-indigo-600", border: "border-indigo-100" };
        default: return { bg: "bg-gray-50", text: "text-gray-600", border: "border-gray-100" };
      }
    };

    const NoteIcon = getNoteTypeIcon(note.note_type);
    const noteTypeColors = getNoteTypeColor(note.note_type);

    return (
      <div
        key={note.id}
        className={`group relative bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer hover:bg-gray-50`}
        onClick={() => handleViewNote(note)}
        onMouseEnter={() => setHoveredNote(note.id)}
        onMouseLeave={() => setHoveredNote(null)}
      >
        <div className="px-4 pt-4 pb-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <NoteIcon className={`h-5 w-5 ${noteTypeColors.text}`} />
              <span className={`text-xs font-semibold ${noteTypeColors.text} ${noteTypeColors.bg} px-2 py-1 rounded-full border ${noteTypeColors.border}`}>
                {note.note_type}
              </span>
            </div>
            <div className="flex gap-1.5">
              {note.is_pinned && <Pin className="h-4 w-4 text-yellow-500" />}
              {note.is_private && <Lock className="h-4 w-4 text-red-500" />}
            </div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
            {note.title}
          </h3>
          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
            {note.content || "No content available"}
          </p>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1.5 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              {note.changeLabel}
            </div>
            {note.entity_name && (
              <div className="flex items-center gap-1.5 text-xs text-gray-600">
                <UserCheck className="h-3 w-3" />
                <span className="truncate">{note.entity_name}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Screen>
      <div className="relative overflow-hidden bg-gradient-to-br from-indigo-600 to-purple-500 text-white">
        <div className="relative container mx-auto px-4 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 mb-1">
                <div className="p-1.5 bg-white/20 rounded-lg backdrop-blur-sm">
                  <Activity className="h-5 w-5 text-white" />
                </div>
                <div className="h-0.5 w-8 bg-gradient-to-r from-white/40 to-transparent rounded-full"></div>
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-white to-indigo-100 bg-clip-text text-transparent">
                My Notes Dashboard
              </h1>
              <p className="text-sm text-indigo-100 max-w-md">
                Manage notes across customer, sales, and prospect categories
              </p>
            </div>
            <div className="flex gap-2">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/20">
                <div className="flex items-center gap-2">
                  <div className="p-1.5 bg-white/20 rounded-md">
                    <BarChart3 className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="text-white/90 text-xs font-medium">Total Notes</p>
                    <p className="text-lg font-bold text-white">
                      {customerNotesData.length + salesNotesData.length + prospectsNotesData.length}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="container mx-auto px-6 py-6">
        <Tabs
          value={activeCategory}
          onValueChange={(value) => setActiveCategory(value as any)}
          className="w-full"
        >
          <TabsList className="flex w-full mb-4 bg-gray-100 border-b border-gray-200">
            <TabsTrigger
              value="Customer"
              className="flex-1 text-center py-2 px-4 text-gray-600 border border-green-500 data-[state=active]:bg-white data-[state=active]:text-black data-[state=active]:border-b-2 data-[state=active]:border-green-500 hover:bg-gray-200 transition-colors duration-150"
            >
              Customer ({customerNotesData.length})
            </TabsTrigger>
            <TabsTrigger
              value="Lead File"
              className="flex-1 text-center py-2 px-4 text-gray-600 border border-green-500 data-[state=active]:bg-white data-[state=active]:text-black data-[state=active]:border-b-2 data-[state=active]:border-green-500 hover:bg-gray-200 transition-colors duration-150"
            >
              Sales ({salesNotesData.length})
            </TabsTrigger>
            <TabsTrigger
              value="Prospect"
              className="flex-1 text-center py-2 px-4 text-gray-600 border border-green-500 data-[state=active]:bg-white data-[state=active]:text-black data-[state=active]:border-b-2 data-[state=active]:border-green-500 hover:bg-gray-200 transition-colors duration-150"
            >
              Prospects ({prospectsNotesData.length})
            </TabsTrigger>
          </TabsList>
          <TabsContent value="Customer" className="space-y-6">
            {customerLoading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="animate-pulse h-32 bg-gray-50 border border-gray-200 rounded-lg shadow-sm"
                  >
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="h-5 w-5 bg-gray-200 rounded-full"></div>
                        <div className="h-4 w-16 bg-gray-200 rounded"></div>
                      </div>
                      <div className="h-4 w-3/4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 w-5/6 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : customerNotesData.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200 shadow-sm">
                <UserCheck className="h-10 w-10 text-indigo-500 mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No customer notes yet
                </h3>
                <p className="text-sm text-gray-600 max-w-md mx-auto">
                  You haven't created any customer notes yet.
                </p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {customerNotesData.map((note, index) =>
                  renderNoteCard(note, index)
                )}
              </div>
            )}
          </TabsContent>
          <TabsContent value="Lead File" className="space-y-6">
            {salesLoading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="animate-pulse h-32 bg-gray-50 border border-gray-200 rounded-lg shadow-sm"
                  >
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="h-5 w-5 bg-gray-200 rounded-full"></div>
                        <div className="h-4 w-16 bg-gray-200 rounded"></div>
                      </div>
                      <div className="h-4 w-3/4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 w-5/6 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : salesNotesData.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200 shadow-sm">
                <TrendingUp className="h-10 w-10 text-purple-500 mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No sales notes yet
                </h3>
                <p className="text-sm text-gray-600 max-w-md mx-auto">
                  You haven't created any sales notes yet.
                </p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {salesNotesData.map((note, index) =>
                  renderNoteCard(note, index)
                )}
              </div>
            )}
          </TabsContent>
          <TabsContent value="Prospect" className="space-y-6">
            {prospectsLoading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="animate-pulse h-32 bg-gray-50 border border-gray-200 rounded-lg shadow-sm"
                  >
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="h-5 w-5 bg-gray-200 rounded-full"></div>
                        <div className="h-4 w-16 bg-gray-200 rounded"></div>
                      </div>
                      <div className="h-4 w-3/4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 w-5/6 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : prospectsNotesData.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-200 shadow-sm">
                <Target className="h-10 w-10 text-pink-500 mx-auto mb-3" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  No prospect notes yet
                </h3>
                <p className="text-sm text-gray-600 max-w-md mx-auto">
                  You haven't created any prospect notes yet.
                </p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {prospectsNotesData.map((note, index) =>
                  renderNoteCard(note, index)
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
      <ViewNoteModal
        isOpen={isViewModalOpen}
        onOpenChange={setIsViewModalOpen}
        noteData={
          selectedNote
            ? {
                id: parseInt(selectedNote.id.replace('NOTE-', ''), 16) || Math.random(),
                title: selectedNote.title,
                value: selectedNote.note_type,
                icon: FileText,
                change: selectedNote.change,
                changeLabel: selectedNote.changeLabel,
                description: selectedNote.content,
                isPinned: selectedNote.is_pinned,
                isPrivate: selectedNote.is_private,
                attachment: null,
                createdAt: selectedNote.created_at,
                updatedAt: selectedNote.updated_at,
                author: selectedNote.entity_name || "Unknown",
                category: selectedNote.entity_type,
                tags: [], // Default empty array for tags
                priority: "low", // Default priority
                wordCount: selectedNote.content.split(' ').length,
                readTime: `${Math.ceil(selectedNote.content.split(' ').length / 200)} min read`,
              }
            : null
        }
        onSubmit={handleNoteUpdate}
      />
    </Screen>
  );
}

export default ViewNotes;