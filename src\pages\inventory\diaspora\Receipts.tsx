import { useState } from "react";
import { Eye, Download, X, CheckC<PERSON>ckIcon } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { Screen } from "@/app-components/layout/screen";
import {
  useGetDiasporaRecieptsQuery,
  useGetPlotBookingsQuery,
  usePatchDiasporaRecieptsMutation,
} from "@/redux/slices/projects";
import ReceiptDetailsModal from "./ReceiptDetailsModal";
import { formatDateTime } from "@/utils/formatDate";
import { toast } from "sonner";
import ConfirmationModal from "@/components/custom/modals/ConfirmationModal";

export interface PlotBooking {
  id: string;
  plots: string;
  marketer: string;
  marketer_name: string;
  customer_name: string;
  customer: string;
  booking_type: string;
  booking_id: number;
  amount: string;
  transaction_id: string;
  transportation_transaction_id: string;
  description: string;
  office: string;
  proof_of_payment: string;
  upload_time: string;
  status: string;
  deadline: string;
  creation_date: string;
  expected_payment_date: string;
}

export default function Receipts() {
  const { data: bookings, isLoading } = useGetDiasporaRecieptsQuery({});
  const [cancelReceipt, { isLoading: cancelling }] =
    usePatchDiasporaRecieptsMutation();

  const [selectedBooking, setSelectedBooking] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  const [selectedReceiptId, setSelectedReceiptId] = useState<string | null>(
    null
  );
  const [selectedReceiptPlot, setSelectedReceiptPlot] = useState<string | null>(
    null
  );
  const [isOpenCancelModal, setIsOpenCancelModal] = useState(false);

  const handleCancelReciept = async () => {
    if (selectedReceiptId) {
      const res = await cancelReceipt({
        id: selectedReceiptId,
        action: "cancel",
      }).unwrap();
      if (res?.success) {
        toast.success("Receipt cancelled successfully");
        setSelectedReceiptId(null);
        setIsOpenCancelModal(false);
      } else {
        toast.error("Failed to cancel receipt");
        return;
      }
    } else {
      toast.error("Please select a receipt to cancel");
      return;
    }
  };

  const handleConfirmCancel = (id: string, plot: string) => {
    setSelectedReceiptId(id);
    setSelectedReceiptPlot(plot);
    setIsOpenCancelModal(true);
  };

  useState(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 768);
    };
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  });

  const openDetailsModal = (booking: any) => {
    setSelectedBooking(booking);
    setIsModalOpen(true);
  };

  const getMobileColumns = (): ColumnDef<any>[] => [
    {
      accessorKey: "plot_no",
      header: "Plot",
      cell: (info) => (
        <span className="font-medium">{info.getValue() as string}</span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "client_name",
      header: "Client",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: (info) => {
        return (
          <div className="flex flex-col">
            <span className="font-medium">
              {(info.getValue() as number).toLocaleString()}
            </span>
            <span className="text-xs text-gray-500">
              {info.row.original.amount}
            </span>
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                openDetailsModal(booking);
              }}
              className="h-8 w-8 p-0"
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">View details</span>
            </Button>

            {!booking.is_cancelled && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleConfirmCancel(booking.id, booking.plot_no);
                }}
                className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Cancel Receipt</span>
              </Button>
            )}
          </>
        );
      },
      enableColumnFilter: false,
    },
  ];

  const getDesktopColumns = (): ColumnDef<any>[] => [
    {
      accessorKey: "plot_no",
      header: "Plot No",
      cell: (info) => (
        <span className="font-medium">{info.getValue() as string}</span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "marketer",
      header: "Marketer",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "client_name",
      header: "Client",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "amount",
      header: "Amount Paid",
      cell: (info) => {
        const booking = info.row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">
              {(info.getValue() as number).toLocaleString()}
            </span>
            <span className="text-xs text-gray-500">
              Pay Mode - {booking?.payment_mode}
            </span>
            {booking.acc_no != "N/A" && (
              <span className="text-xs text-gray-500">
                A/c No - {booking.acc_no}
              </span>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "receipt",
      header: "Attachments",
      cell: (info) => {
        const booking = info.row.original;
        return (
          <div className="flex flex-col space-y-1">
            {booking.receipt && (
              <a
                href={booking.receipt}
                target="_blank"
                rel="noopener noreferrer"
                download
                className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
              >
                <span className="material-icons text-blue-500 mr-1 text-xl">
                  &#x1F4CE;
                </span>
                Open Proof Of Payment
              </a>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "country",
      header: "Country",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "is_cancelled",
      header: "Status",
      cell: (info) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-semibold ${
            info.getValue() === true
              ? "bg-red-100 text-red-800"
              : "bg-green-100 text-green-800"
          }`}
        >
          {info.getValue() === true ? "Cancelled" : "Active"}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell: (info) => formatDateTime(info.getValue() as string),
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                openDetailsModal(booking);
              }}
              className="h-8 w-8 p-0"
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">View details</span>
            </Button>
            {!booking.is_cancelled && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  handleConfirmCancel(booking.id, booking.plot_no);
                }}
                className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Cancel Receipt</span>
              </Button>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];
  const columns = isSmallScreen ? getMobileColumns() : getDesktopColumns();

  return (
    <Screen>
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border dark:bg-gray-800 dark:text-white">
          <div className="p-4 border-b">
            <h1 className="text-lg md:text-xl lg:text-2xl font-semibold text-gray-800 dark:text-white">
              {isSmallScreen
                ? "DIASPORA BOOKINGS"
                : "RECEIPTED DIASPORA SPECIAL BOOKINGS"}
            </h1>
          </div>
          <div className="p-2 md:p-4">
            <div className="overflow-x-auto">
              <DataTable<any>
                data={bookings?.data ? bookings.data.results : []}
                columns={columns}
                enableToolbar={true}
                enableExportToExcel={!isSmallScreen}
                enablePrintPdf={!isSmallScreen}
                enablePagination={true}
                enableColumnFilters={!isSmallScreen}
                enableSorting={true}
                searchInput={<SearchComponent />}
                tHeadClassName="bg-secondary"
              />
            </div>
          </div>
        </div>
        <ReceiptDetailsModal
          isOpen={isModalOpen}
          onOpenChange={setIsModalOpen}
          selectedBooking={selectedBooking}
          isOpenCancelModal={isOpenCancelModal}
          setIsOpenCancelModal={setIsOpenCancelModal}
          handleConfirmCancel={handleConfirmCancel}
          handleCancelReciept={handleCancelReciept}
          openDetailsModal
        />

        <ConfirmationModal
          isOpen={isOpenCancelModal}
          onOpenChange={setIsOpenCancelModal}
          title="Cancel Receipt"
          description={`Are you sure you want to cancel this receipt for plot ${selectedReceiptPlot}?`}
          // confirmButtonLabel="Cancel Receipt"
          // confirmButtonColor="red"
          onConfirm={handleCancelReciept}
        />
      </div>
    </Screen>
  );
}

function SearchComponent() {
  const [universalSearchValue, setUniversalSearchValue] = useState("");
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  return (
    <input
      value={universalSearchValue}
      onChange={(e) => setUniversalSearchValue(e.target.value)}
      className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
      placeholder={
        isSmallScreen ? "Search..." : "Search plots, clients, marketers..."
      }
    />
  );
}
