import React, { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Building2,
  TrendingUp,
  TrendingDown,
  UserCheck,
  UserX,
  Calendar,
  BarChart3,
  PieChart,
  Target,
  Award,
  Clock,
  DollarSign,
  Activity,
  Eye,
  ExternalLink,
  Filter,
  Download,
  RefreshCw,
  UserPlus,
  Briefcase,
  Heart,
  Globe
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { CustomActiveShapePieChart } from "@/components/custom/charts/PieChartVariants";
import { SimpleFunnelChart } from "@/components/custom/charts/FunnelChartVariants";
import { CustomShapeBarChart } from "@/components/custom/charts/BarChartVariants";

// Sample data for the dashboard
const sampleData = {
  departments: [
    { name: "Sales", employees: 45, active: 42, inactive: 3 },
    { name: "Marketing", employees: 28, active: 26, inactive: 2 },
    { name: "H<PERSON>", employees: 15, active: 15, inactive: 0 },
    { name: "Finance", employees: 20, active: 19, inactive: 1 },
    { name: "IT", employees: 32, active: 30, inactive: 2 },
    { name: "Operations", employees: 38, active: 35, inactive: 3 },
    { name: "Legal", employees: 12, active: 12, inactive: 0 },
    { name: "Customer Service", employees: 25, active: 23, inactive: 2 }
  ],
  demographics: {
    gender: { male: 125, female: 90, other: 0 },
    ethnicity: {
      "African": 180,
      "Asian": 20,
      "Caucasian": 10,
      "Mixed": 5
    },
    maritalStatus: {
      "Single": 95,
      "Married": 105,
      "Divorced": 10,
      "Widowed": 5
    }
  },
  recruitment: {
    applications: 450,
    screening: 180,
    interviews: 85,
    offers: 35,
    hired: 28
  },
  leave: {
    totalDays: 1250,
    approved: 1100,
    pending: 85,
    rejected: 65,
    types: {
      "Annual": 650,
      "Sick": 280,
      "Maternity": 120,
      "Emergency": 200
    }
  },
  turnover: {
    overall: 8.5,
    departments: [
      { name: "Sales", rate: 12.3 },
      { name: "Marketing", rate: 6.8 },
      { name: "HR", rate: 4.2 },
      { name: "Finance", rate: 5.1 },
      { name: "IT", rate: 9.7 },
      { name: "Operations", rate: 7.9 },
      { name: "Legal", rate: 3.5 },
      { name: "Customer Service", rate: 11.2 }
    ]
  },
  performance: {
    excellent: 45,
    good: 120,
    satisfactory: 40,
    needsImprovement: 10
  }
};

interface OrganizationChartModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const OrganizationChartModal: React.FC<OrganizationChartModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">Organization Chart</h2>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
        <div className="text-center">
          <p className="mb-4">Organization chart will be displayed here</p>
          <Button variant="outline" className="mb-4">
            <ExternalLink className="w-4 h-4 mr-2" />
            View Full Organization Chart
          </Button>
          <div className="bg-gray-100 dark:bg-gray-700 p-8 rounded-lg">
            <p className="text-gray-600 dark:text-gray-400">
              Interactive organization chart placeholder
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const DirectorsDashboard: React.FC = () => {
  const [showOrgChart, setShowOrgChart] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState("monthly");

  const totalEmployees = sampleData.departments.reduce((sum, dept) => sum + dept.employees, 0);
  const activeEmployees = sampleData.departments.reduce((sum, dept) => sum + dept.active, 0);
  const inactiveEmployees = totalEmployees - activeEmployees;

  // Key metrics for the top cards
  const keyMetrics = [
    {
      title: "Total Departments",
      value: sampleData.departments.length,
      icon: Building2,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      change: "+2 this year",
      trend: "up" as const
    },
    {
      title: "Active Employees",
      value: activeEmployees,
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      change: "+12 this month",
      trend: "up" as const
    },
    {
      title: "Employee Turnover",
      value: `${sampleData.turnover.overall}%`,
      icon: TrendingDown,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200",
      change: "-1.2% vs last quarter",
      trend: "down" as const
    },
    {
      title: "Performance Rating",
      value: "4.2/5",
      icon: Award,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      change: "+0.3 improvement",
      trend: "up" as const
    }
  ];

  return (
    <Screen>
      <div className="space-y-6">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-blue-100 dark:border-gray-600">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Directors Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Comprehensive overview of organizational metrics and analytics
              </p>
              <div className="flex items-center gap-4 mt-3">
                <Badge variant="outline" className="bg-white dark:bg-gray-800">
                  <Activity className="w-3 h-3 mr-1" />
                  Live Data
                </Badge>
                <Badge variant="outline" className="bg-white dark:bg-gray-800">
                  <Clock className="w-3 h-3 mr-1" />
                  Last updated: {new Date().toLocaleTimeString()}
                </Badge>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="hover:bg-blue-50 dark:hover:bg-gray-700">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm" className="hover:bg-green-50 dark:hover:bg-gray-700">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm" className="hover:bg-purple-50 dark:hover:bg-gray-700">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Key Metrics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {keyMetrics.map((metric, index) => (
            <Card key={metric.title} className={`border-2 ${metric.borderColor} ${metric.bgColor} hover:shadow-lg transition-all duration-200`}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {metric.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {metric.value}
                    </p>
                    <div className="flex items-center gap-1">
                      {metric.trend === "up" ? (
                        <TrendingUp className="w-3 h-3 text-green-500" />
                      ) : (
                        <TrendingDown className="w-3 h-3 text-red-500" />
                      )}
                      <span className={`text-xs ${metric.trend === "up" ? "text-green-600" : "text-red-600"}`}>
                        {metric.change}
                      </span>
                    </div>
                  </div>
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${metric.bgColor} border ${metric.borderColor}`}>
                    <metric.icon className={`w-6 h-6 ${metric.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Organization Chart Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Organization Chart
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Button 
                onClick={() => setShowOrgChart(true)}
                className="mb-4"
              >
                <Eye className="w-4 h-4 mr-2" />
                View Organization Chart
              </Button>
              <p className="text-gray-600 dark:text-gray-400">
                Click to view the complete organizational structure
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Demographics and Recruitment Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Demographics Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Demographics Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Gender Distribution */}
                <div>
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <UserCheck className="w-4 h-4" />
                    Gender Distribution
                  </h4>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{sampleData.demographics.gender.male}</div>
                      <div className="text-sm text-gray-600">Male</div>
                      <div className="text-xs text-gray-500">
                        {((sampleData.demographics.gender.male / totalEmployees) * 100).toFixed(1)}%
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-pink-600">{sampleData.demographics.gender.female}</div>
                      <div className="text-sm text-gray-600">Female</div>
                      <div className="text-xs text-gray-500">
                        {((sampleData.demographics.gender.female / totalEmployees) * 100).toFixed(1)}%
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">{sampleData.demographics.gender.other}</div>
                      <div className="text-sm text-gray-600">Other</div>
                      <div className="text-xs text-gray-500">
                        {((sampleData.demographics.gender.other / totalEmployees) * 100).toFixed(1)}%
                      </div>
                    </div>
                  </div>
                </div>

                {/* Marital Status */}
                <div>
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <Heart className="w-4 h-4" />
                    Marital Status
                  </h4>
                  <div className="space-y-2">
                    {Object.entries(sampleData.demographics.maritalStatus).map(([status, count]) => (
                      <div key={status} className="flex justify-between items-center">
                        <span className="text-sm">{status}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${(count / totalEmployees) * 100}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium w-8">{count}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recruitment Funnel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserPlus className="w-5 h-5" />
                Recruitment Funnel
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(sampleData.recruitment).map(([stage, count], index) => {
                  const percentage = index === 0 ? 100 : ((count / sampleData.recruitment.applications) * 100);
                  const stageNames = {
                    applications: "Applications",
                    screening: "Screening",
                    interviews: "Interviews",
                    offers: "Offers",
                    hired: "Hired"
                  };

                  return (
                    <div key={stage} className="flex items-center gap-4">
                      <div className="w-24 text-sm font-medium">
                        {stageNames[stage as keyof typeof stageNames]}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-center mb-1">
                          <div className="text-sm text-gray-600">{percentage.toFixed(1)}%</div>
                          <div className="text-sm font-bold">{count}</div>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div
                            className={`h-3 rounded-full transition-all duration-500 ${
                              index === 0 ? 'bg-blue-500' :
                              index === 1 ? 'bg-green-500' :
                              index === 2 ? 'bg-yellow-500' :
                              index === 3 ? 'bg-orange-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Department Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Department Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="w-5 h-5" />
                Department Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {sampleData.departments.map((dept) => (
                  <div key={dept.name} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <div className="font-medium">{dept.name}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {dept.employees} employees
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          {dept.active} Active
                        </Badge>
                        {dept.inactive > 0 && (
                          <Badge variant="outline" className="text-red-600 border-red-200">
                            {dept.inactive} Inactive
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Employee Turnover by Department */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingDown className="w-5 h-5" />
                Turnover by Department
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {sampleData.turnover.departments.map((dept) => (
                  <div key={dept.name} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{dept.name}</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            dept.rate > 10 ? 'bg-red-500' :
                            dept.rate > 7 ? 'bg-yellow-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${(dept.rate / 15) * 100}%` }}
                        ></div>
                      </div>
                      <span className={`text-sm font-bold ${
                        dept.rate > 10 ? 'text-red-600' :
                        dept.rate > 7 ? 'text-yellow-600' : 'text-green-600'
                      }`}>
                        {dept.rate}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Ethnicity Distribution Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              Ethnicity Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {Object.entries(sampleData.demographics.ethnicity).map(([ethnicity, count]) => (
                <div key={ethnicity} className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-xl font-bold text-blue-600">{count}</div>
                  <div className="text-sm text-gray-600">{ethnicity}</div>
                  <div className="text-xs text-gray-500">
                    {((count / totalEmployees) * 100).toFixed(1)}%
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Leave Analytics and Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Leave Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Leave Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Leave Summary */}
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-green-600">{sampleData.leave.approved}</div>
                    <div className="text-sm text-gray-600">Approved</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-yellow-600">{sampleData.leave.pending}</div>
                    <div className="text-sm text-gray-600">Pending</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-600">{sampleData.leave.rejected}</div>
                    <div className="text-sm text-gray-600">Rejected</div>
                  </div>
                </div>

                {/* Leave Types */}
                <div>
                  <h4 className="font-semibold mb-3">Leave Types</h4>
                  <div className="space-y-2">
                    {Object.entries(sampleData.leave.types).map(([type, days]) => (
                      <div key={type} className="flex justify-between items-center">
                        <span className="text-sm">{type}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${(days / sampleData.leave.totalDays) * 100}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium w-8">{days}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5" />
                Performance Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Performance Distribution */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{sampleData.performance.excellent}</div>
                    <div className="text-sm text-green-700 dark:text-green-400">Excellent</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{sampleData.performance.good}</div>
                    <div className="text-sm text-blue-700 dark:text-blue-400">Good</div>
                  </div>
                  <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">{sampleData.performance.satisfactory}</div>
                    <div className="text-sm text-yellow-700 dark:text-yellow-400">Satisfactory</div>
                  </div>
                  <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">{sampleData.performance.needsImprovement}</div>
                    <div className="text-sm text-red-700 dark:text-red-400">Needs Improvement</div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="pt-4 border-t">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Overall Performance Score</span>
                    <span className="text-lg font-bold text-green-600">4.2/5.0</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div className="bg-green-500 h-3 rounded-full" style={{ width: '84%' }}></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Department Gender Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Department Gender Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {sampleData.departments.map((dept) => {
                // Sample gender distribution for each department
                const maleRatio = Math.random() * 0.4 + 0.3; // 30-70%
                const femaleRatio = 1 - maleRatio;
                const maleCount = Math.round(dept.employees * maleRatio);
                const femaleCount = dept.employees - maleCount;

                return (
                  <div key={dept.name} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">{dept.name}</span>
                      <span className="text-sm text-gray-600">{dept.employees} total</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex-1">
                        <div className="flex justify-between text-xs mb-1">
                          <span>Male: {maleCount}</span>
                          <span>Female: {femaleCount}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3 flex overflow-hidden">
                          <div
                            className="bg-blue-500 h-3"
                            style={{ width: `${maleRatio * 100}%` }}
                          ></div>
                          <div
                            className="bg-pink-500 h-3"
                            style={{ width: `${femaleRatio * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="text-right text-xs">
                        <div className="text-blue-600">{(maleRatio * 100).toFixed(0)}%</div>
                        <div className="text-pink-600">{(femaleRatio * 100).toFixed(0)}%</div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Performance Trends and Additional Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Performance Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Performance Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">4.2</div>
                  <div className="text-sm text-gray-600">Average Rating</div>
                  <div className="flex items-center justify-center gap-1 mt-1">
                    <TrendingUp className="w-3 h-3 text-green-500" />
                    <span className="text-xs text-green-600">+0.3 from last quarter</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Goal Achievement</span>
                    <span className="font-medium">87%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '87%' }}></div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Training Completion</span>
                    <span className="font-medium">92%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" />
                Quick Stats
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Avg. Tenure</span>
                  <span className="font-bold">3.2 years</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Promotion Rate</span>
                  <span className="font-bold text-green-600">15%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Training Hours</span>
                  <span className="font-bold">2,450</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Satisfaction Score</span>
                  <span className="font-bold text-blue-600">4.1/5</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Absenteeism Rate</span>
                  <span className="font-bold text-orange-600">3.2%</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                Action Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border-l-4 border-red-500">
                  <div className="text-sm font-medium text-red-800 dark:text-red-400">High Priority</div>
                  <div className="text-xs text-red-600 dark:text-red-300">Address turnover in Sales dept</div>
                </div>
                <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border-l-4 border-yellow-500">
                  <div className="text-sm font-medium text-yellow-800 dark:text-yellow-400">Medium Priority</div>
                  <div className="text-xs text-yellow-600 dark:text-yellow-300">Review IT performance metrics</div>
                </div>
                <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border-l-4 border-green-500">
                  <div className="text-sm font-medium text-green-800 dark:text-green-400">Low Priority</div>
                  <div className="text-xs text-green-600 dark:text-green-300">Update training programs</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Organization Chart Modal */}
        <OrganizationChartModal
          isOpen={showOrgChart}
          onClose={() => setShowOrgChart(false)}
        />
      </div>
    </Screen>
  );
};

export default DirectorsDashboard;
