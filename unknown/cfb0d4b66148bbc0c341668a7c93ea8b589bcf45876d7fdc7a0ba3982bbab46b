import { Screen } from "@/app-components/layout/screen";
import { Badge } from "@/components/ui/badge";
import {
  AlignEndHorizontal,
  ChartArea,
  ChartBarBig,
  ChartPie,
} from "lucide-react";
import React, { useState } from "react";
import AllBookings from "./Modals/AllBookings";

const index = () => {
  const [allBookingModal, setAllBookingModal] = useState<boolean>(false);

  const items = [
    {
      color: "emerald",
      icon: <ChartBarBig size={24} />,
      title: "All Bookings",
      subtitle: "filter all bookings report",
      badgeText: "View",
      onClick: () => setAllBookingModal(true),
    },
  ];

  return (
    <Screen>
      <div className="flex justify-between items-center mb-4 py-6'">
        <h1 className="text-3xl font-bold text-gray-800 px-4">Reports</h1>
      </div>
      <div className="grid md:grid-cols-3 grid-cols-1 gap-4 px-3">
        {items.map((item, idx) => (
          <div
            key={idx}
            className={`flex items-center gap-2 mb-4 bg-${item.color}-50 dark:bg-${item.color}-800/60 px-3 py-5 rounded-xl cursor-pointer`}
            onClick={item.onClick}
          >
            <div className={`bg-${item.color}-600 p-2 rounded-lg text-white`}>
              {item.icon}
            </div>
            <div>
              <p className="font-bold text-sm">{item.title}</p>
              <p className="text-xs">{item.subtitle}</p>
            </div>
            <Badge
              className={`bg-${item.color}-600 hover:bg-${item.color}-600 ml-auto px-4 hover:underline`}
            >
              <p>{item.badgeText}</p>
            </Badge>
          </div>
        ))}
      </div>

      {allBookingModal && (
        <AllBookings
          allBookingModal={allBookingModal}
          setAllBookingModal={setAllBookingModal}
          title="All Bookings Report"
        />
      )}
    </Screen>
  );
};

export default index;
