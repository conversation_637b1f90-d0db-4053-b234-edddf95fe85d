import { useNavigate } from 'react-router-dom';
import { useSidebarPermissions } from './useSidebarPermissions';

// Route to permission mapping (same as in PermissionGuard and PermissionAwareLink)
const ROUTE_PERMISSIONS: Record<string, number[]> = {
  // Main section routes (permission 111)
  '/': [111],
  '/logistics-dash': [111],
  '/logistics': [111],
  '/logistics/vehicles': [111],
  '/drivers': [111],
  '/logistics-stats': [111],
  '/logistics-reports': [210],
  '/book-visit': [111],
  '/all-visits': [111],
  '/logistics/site-visits': [111],
  '/logistics/cashback': [111],
  '/logistics/transactions': [111],
  '/clients': [111],
  '/allsitevisitreport': [211],
  '/sales': [111],
  '/sales/overview': [111],
  '/new-sales': [111],
  '/customers': [111],
  '/customers/overview': [111],
  '/customer': [111],
  '/prospects': [111],
  '/prospect': [111],
  '/inventory': [111], // Main inventory access
  '/projects': [111], // Dashboard access
  '/project': [111], // for dynamic routes like /project/:id
  '/mybookings': [7113, 7117], // My Booking access - requires VIEW_INVENTORY_MARKETER or VIEW_INVENTORY_DIASPORA
  '/inventory/booking-approvals': [7114, 7115], // Special Bookings access - requires GM HQ or GM KAREN
  '/inventory/accounts': [7116], // VIEW_INVENTORY_ACCOUNTS
  '/diaspora-trips': [7117, 7123, 7124], // VIEW_INVENTORY_DIASPORA, DRM, Diaspora Manager Kasaya
  '/diaspora-trips/all': [7124], // All Trips - Diaspora Manager Kasaya only
  '/diaspora-reservations': [7117], // VIEW_INVENTORY_DIASPORA (Marketers)
  '/diaspora-receipts': [7117], // VIEW_INVENTORY_DIASPORA (Marketers)
  '/plotmarketreport': [7118], // VIEW_INVENTORY_REPORTS (Marketers Reports)
  '/inventory/pricing': [7119], // VIEW_INVENTORY_PRICING
  '/inventory/pricing/project-pricing': [7119], // VIEW_INVENTORY_PRICING
  '/inventory/pricing/payment-plan-checker': [7119], // VIEW_INVENTORY_PRICING
  '/mpesa-transactions': [7120], // VIEW_INVENTORY_MPESA_TRANSACTIONS
  '/inventory/logs': [7121], // VIEW_INVENTORY_LOGS
  
  // Performance routes with role-based permissions
  '/performance': [7114, 7115, 7127], // Marketer Performance - GM HQ, GM Karen, Team Leader
  '/marketer-targets': [7113], // My Targets - Marketer only
  '/marketer-commissions': [7113], // My Commissions - Marketer only
  '/cash-on-cash': [7113], // My Cash on Cash - Marketer only
  '/portfolio': [7113, 7114, 7115], // My Portfolio (marketer) + Marketer Portfolio (GMs)
  '/office-performance': [7127, 7114, 7115], // Team Leader and above
  '/targets': [7127, 7114, 7115], // Team Leader and above
  '/commissions': [7127, 7114, 7115], // Team Leader and above
  '/commission report': [7127, 7114, 7115], // Team Leader and above (legacy URL)
  '/team-performance': [7127, 7114, 7115], // Team Leader and above
  '/profiles': [7127, 7114, 7115], // Team Leader and above
  
  // Teams routes (permission 113)
  '/accounts-dashboard': [113],
  '/gm-karen-dashboard': [113],
  '/hq-hos-dashboard': [113],
  '/diaspora-dashboard': [113],
  '/credits-team-dashboard': [113],
  '/marketer-dashboard': [113],
  '/hr-team': [113],
  '/legal-team': [113],
  '/legal-team/offer-letters-dashboard': [113],
  '/data-team': [113],
  '/tele-marketing-team': [113],
  '/digital-team': [113],
  
  // Reports routes (permission 114)
  '/marketerreport': [114],
  '/plotmarketreport': [114],
  '/diasporareports': [114],
  '/money-in-reports': [114],
  '/installment-reports': [114],
  '/project-summary': [114],
  
  // Analytics routes (permission 115)
  '/money-in-analytics': [115],
  '/installment-analytics': [115],
  
  // Services routes (permission 116)
  '/admin/services': [116],
  '/complaints': [116],
  '/feedback': [116],
  '/ticketing': [116],
  '/flags': [116],
  '/engagements': [116],
  '/notifications': [116],
  '/notes': [116],
  '/reminders': [116],
  '/forms': [116],
  '/surveys': [116],
  
  // Admin routes (permission 117)
  '/users-list': [117],
  '/admin/teams': [117],
  '/admin/groups': [117],
  '/permissions': [117],
  '/admin/assign-role': [117],
  '/groups': [117],
  '/admin/departments': [117],
  
  // Profile routes - accessible to all authenticated users
  '/profile': [],

  // System routes - accessible to all authenticated users
  '/unauthorized': [],
};

export const usePermissionNavigation = () => {
  const navigate = useNavigate();
  const { hasPermission } = useSidebarPermissions();

  const checkRoutePermission = (targetPath: string, requiredPermissions?: number[]): boolean => {
    let permissions = requiredPermissions;
    
    if (!permissions) {
      // Check for exact match first
      permissions = ROUTE_PERMISSIONS[targetPath];
      
      // If no exact match, check for partial matches (for dynamic routes)
      if (!permissions) {
        for (const [route, perms] of Object.entries(ROUTE_PERMISSIONS)) {
          if (targetPath.startsWith(route) && route !== '/') {
            permissions = perms;
            break;
          }
        }
      }
      
      // Default to Main section permission if no specific permission found
      if (!permissions) {
        permissions = [111];
      }
    }

    // If no permissions required (empty array), allow access
    if (permissions.length === 0) {
      return true;
    }

    // Check if user has any of the required permissions
    return permissions.some(permission => hasPermission(permission));
  };

  const navigateWithPermissionCheck = (
    targetPath: string, 
    requiredPermissions?: number[],
    options?: { replace?: boolean }
  ) => {
    const hasAccess = checkRoutePermission(targetPath, requiredPermissions);
    
    if (hasAccess) {
      navigate(targetPath, options);
    } else {
      navigate('/unauthorized', { replace: options?.replace });
    }
  };

  const canAccessRoute = (targetPath: string, requiredPermissions?: number[]): boolean => {
    return checkRoutePermission(targetPath, requiredPermissions);
  };

  return {
    navigateWithPermissionCheck,
    canAccessRoute,
    checkRoutePermission,
  };
};
