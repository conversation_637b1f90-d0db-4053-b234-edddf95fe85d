import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import {
  CheckCheck,
  Recycle,
  RefreshCcw,
  Rotate3dIcon,
  Tag,
  User,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  useGetPlotBookingsQuery,
  useUpdatePlotBookingMutation,
} from "@/redux/slices/projects";
import { formatDateTime } from "@/utils/formatDate";
import { toast } from "sonner";
import InputWithTags from "@/components/custom/forms/InputWithTags";
import { searchDebouncer } from "@/utils/debouncers";

interface Booking {
  booking_id: string;
  booking_type: string;
  plots: string;
  amount: string;
  marketer_name: string;
  customer_name: string;
  transaction_id: string;
  creation_date: string;
  proof_of_payment: string;
  upload_time: string;
  type: string;
  status: string;
}

export default function ArchivedBooking() {
  const [searchInput, setSearchInput] = useState(""); // input field value
  const [searchValue, setSearchValue] = useState(""); // search value to send to API
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const { data: bookingsData, isLoading: loading } = useGetPlotBookingsQuery({
    status: "OLD",
    search: searchValue,
  });

  // Column definitions for TanStack React Table
  const columns: ColumnDef<Booking>[] = [
    {
      accessorKey: "Client",
      header: "Client Name",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex items-center gap-3">
            <User size={26} className="text-primary" />
            <div className="flex flex-col gap-1">
              <p>
                <strong>Marketer: </strong> {rowData.marketer_name}{" "}
              </p>
              <p>
                <strong>Client: </strong> {rowData.customer_name}{" "}
              </p>
              <p>
                <strong>Plot(s) No: </strong> {rowData.plots}{" "}
              </p>
            </div>
          </div>
        );
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "mpesaTransaction",
      header: "Mpesa",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex items-center gap-3">
            <Tag className="text-primary" />
            <div className="flex flex-col gap-1">
              <p>
                <strong>Amount Paid: </strong> {rowData.amount}
              </p>
              {rowData.booking_type == "MPESA" && (
                <p>
                  <strong>Mpesa Transactions: </strong>{" "}
                  {rowData.transaction_id || "Not Paid"}
                </p>
              )}
              <p>
                <strong>Date: </strong> {formatDateTime(rowData.creation_date)}
              </p>
            </div>
          </div>
        );
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "proof_of_payment",
      header: "Attachments",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            {rowData?.proof_of_payment ? (
              <>
                <p>
                  <strong>Upload Date:</strong>{" "}
                  {rowData.upload_time || "No Upload"}{" "}
                </p>
                <a
                  href={rowData.proof_of_payment}
                  target="_blank"
                  className="text-blue-600 underline"
                >
                  Open attached file
                </a>
              </>
            ) : (
              <p className="text-destructive">No File Uploaded</p>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "booking_type",
      header: "Type",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "actions",
      header: "Actions",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex gap-4">
            <div title="Activate">
              <Revert booking_id={rowData?.booking_id} />
            </div>
          </div>
        );
      },
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];

  return (
    <Screen>
      <div className=" !m-0 min-h-screen ">
        <div className="border p-3 mb-2 rounded">
          <h2 className=" font-bold text-lg">Archived Bookings</h2>
        </div>
        <DataTable<Booking>
          data={bookingsData?.data?.results || []}
          columns={columns}
          title="All Booking"
          enableSelectColumn={false}
          enableColumnFilters={true}
          enableSorting={true}
          enableToolbar={true}
          enableFullScreenToggle={true}
          enableColumnControl={true}
          tableClassName="border"
          containerClassName=" rounded py-2"
          tBodyCellsClassName="text-xs"
          tHeadClassName="bg-secondary"
          enablePagination={true}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          totalItems={bookingsData?.data?.total_data || 0}
          searchInput={
            <input
              value={searchInput}
              name="searchInput"
              type="search"
              onChange={(e) =>
                searchDebouncer(e.target.value, setSearchInput, setSearchValue)
              }
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search..."
            />
          }
        />
      </div>
    </Screen>
  );
}

function Revert({ booking_id }: { booking_id: string }) {
  const [updateBooking, { isLoading: approving }] =
    useUpdatePlotBookingMutation();
  const [isModelXOpen, setIsModelXOpen] = React.useState(false);

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    let nFormData = { id: booking_id, status: "DONE", action: "activate" };

    const newFormData = new FormData();
    Object.entries(nFormData).forEach(([key, value]: [string, unknown]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach((item) => newFormData.append(key, item));
        } else if (value instanceof Date) {
          newFormData.append(key, value.toISOString());
        } else {
          newFormData.append(key, value as string | Blob);
        }
      }
    });

    try {
      const res = await updateBooking({
        id: booking_id,
        formData: newFormData,
      }).unwrap();
      if (res?.success) {
        toast.success("Booking activated successfully");
        setIsModelXOpen(false);
      } else {
        toast.error("Failed to activate booking:");
        return;
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(
        error?.data
          ? error?.data?.error
          : "Something went wrong, please try again"
      );
      return;
    }
  }

  return (
    <>
      <Rotate3dIcon
        size={25}
        className="text-destructive cursor-pointer hover:text-destructive/80"
        onClick={() => setIsModelXOpen(true)}
      />

      <BaseModal
        className="!p-0"
        size="lg"
        isOpen={isModelXOpen}
        onOpenChange={setIsModelXOpen}
        title="Archive Booking"
        headerClassName="px-4 pt-5"
      >
        <div className="px-4 pt-2 pb-10 dark:bg-black/40">
          <form onSubmit={onSubmit} className="space-y-3">
            <p>Are you sure you want to activate this booking??</p>

            <div className="flex flex-wrap items-center justify-end !mt-6 gap-3">
              <PrimaryButton
                type="button"
                className=" "
                onClick={() => setIsModelXOpen(false)}
              >
                Cancel
              </PrimaryButton>
              <PrimaryButton
                type={approving ? "button" : "submit"}
                className=" "
              >
                {approving ? "Activating..." : "Activate"}
              </PrimaryButton>
            </div>
          </form>
        </div>
      </BaseModal>
    </>
  );
}

interface SearchComponentProps {
  universalSearchValue: string;
  setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>;
}

function SearchComponent({
  universalSearchValue,
  setuniversalSearchValue,
}: SearchComponentProps) {
  return (
    <input
      value={universalSearchValue}
      onChange={(e) => setuniversalSearchValue(e.target.value)}
      className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none"
      placeholder="Search..."
    />
  );
}
