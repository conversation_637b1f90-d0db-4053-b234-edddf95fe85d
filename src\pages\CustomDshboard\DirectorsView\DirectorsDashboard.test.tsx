import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import DirectorsDashboard from './DirectorsDashboard';
import '@testing-library/jest-dom';

// Mock the Screen component
jest.mock('@/app-components/layout/screen', () => {
  return {
    Screen: ({ children }: { children: React.ReactNode }) => <div data-testid="screen">{children}</div>
  };
});

// Mock CSS import
jest.mock('./DirectorsDashboard.css', () => ({}));

// Wrapper component for router
const RouterWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe('DirectorsDashboard', () => {
  beforeEach(() => {
    // Mock Date.now() for consistent timestamps
    jest.spyOn(Date.prototype, 'toLocaleTimeString').mockReturnValue('10:30:45 AM');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('renders dashboard title and description', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    expect(screen.getByText('Directors Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Comprehensive overview of organizational metrics and analytics')).toBeInTheDocument();
  });

  test('displays key metrics cards', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    // Check for key metrics
    expect(screen.getByText('Total Departments')).toBeInTheDocument();
    expect(screen.getByText('Active Employees')).toBeInTheDocument();
    expect(screen.getByText('Employee Turnover')).toBeInTheDocument();
    expect(screen.getByText('Performance Rating')).toBeInTheDocument();
  });

  test('shows demographics section', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    expect(screen.getByText('Demographics Overview')).toBeInTheDocument();
    expect(screen.getByText('Gender Distribution')).toBeInTheDocument();
    expect(screen.getByText('Marital Status')).toBeInTheDocument();
  });

  test('displays recruitment funnel', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    expect(screen.getByText('Recruitment Funnel')).toBeInTheDocument();
    expect(screen.getByText('Applications')).toBeInTheDocument();
    expect(screen.getByText('Screening')).toBeInTheDocument();
    expect(screen.getByText('Interviews')).toBeInTheDocument();
  });

  test('shows organization chart section', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    expect(screen.getByText('Organization Chart')).toBeInTheDocument();
    expect(screen.getByText('View Organization Chart')).toBeInTheDocument();
  });

  test('opens organization chart modal when button is clicked', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    const viewChartButton = screen.getByText('View Organization Chart');
    fireEvent.click(viewChartButton);

    // Check if modal is opened
    expect(screen.getByText('Interactive organizational structure')).toBeInTheDocument();
    expect(screen.getByText('Close')).toBeInTheDocument();
  });

  test('closes organization chart modal when close button is clicked', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    // Open modal
    const viewChartButton = screen.getByText('View Organization Chart');
    fireEvent.click(viewChartButton);

    // Close modal
    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);

    // Check if modal is closed
    expect(screen.queryByText('Interactive organizational structure')).not.toBeInTheDocument();
  });

  test('displays department analytics', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    expect(screen.getByText('Department Overview')).toBeInTheDocument();
    expect(screen.getByText('Turnover by Department')).toBeInTheDocument();
  });

  test('shows leave analytics', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    expect(screen.getByText('Leave Analytics')).toBeInTheDocument();
    expect(screen.getByText('Approved')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('Rejected')).toBeInTheDocument();
  });

  test('displays performance analytics', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    expect(screen.getByText('Performance Analytics')).toBeInTheDocument();
    expect(screen.getByText('Excellent')).toBeInTheDocument();
    expect(screen.getByText('Good')).toBeInTheDocument();
    expect(screen.getByText('Satisfactory')).toBeInTheDocument();
  });

  test('shows training and development section', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    expect(screen.getByText('Training & Development')).toBeInTheDocument();
    expect(screen.getByText('Total Programs')).toBeInTheDocument();
    expect(screen.getByText('Completion Rate')).toBeInTheDocument();
  });

  test('displays executive summary', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    expect(screen.getByText('Executive Summary & Key Insights')).toBeInTheDocument();
    expect(screen.getByText('Workforce Overview')).toBeInTheDocument();
    expect(screen.getByText('Performance Highlights')).toBeInTheDocument();
    expect(screen.getByText('Key Recommendations')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    render(
      <RouterWrapper>
        <DirectorsDashboard />
      </RouterWrapper>
    );

    const screen_element = screen.getByTestId('screen');
    expect(screen_element).toBeInTheDocument();
  });
});
